<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AuditLog;

class AuditController extends Controller
{
    public function index(Request $request)
    {
        $records = AuditLog::with('user:id,name')->latest();
        if(isset($request->module)){
            $records = $records->where('module','LIKE', "%$request->module%");
        }
        if(isset($request->action)){
            $records = $records->where('action','LIKE', "%$request->action%");
        }
        if(isset($request->user_name)){
            $records = $records->whereHas('user', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->user_name . '%');
            });
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.audit-logs.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.audit-logs.index',compact('records'));
    }
}
