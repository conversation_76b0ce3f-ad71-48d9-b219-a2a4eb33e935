<?php

namespace App\Http\Controllers;

use App\Models\ClassSubject;
use App\Models\SchoolClass;
use App\Models\Subject;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Http\Request;

class ClassSubjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $classes = SchoolClass::pluck('name','id');
        $subjects = Subject::pluck('name','id');
        $records = ClassSubject::latest();
        if(isset($request->class_name)){
            $records = $records->whereHas('class', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->class_name . '%');
            });
        }
        if(isset($request->subject_name)){
            $records = $records->whereHas('subject', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->subject_name . '%');
            });
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.class-subjects.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.class-subjects.index',compact('records','classes','subjects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'subject_id' => [
                'required',
                'exists:subjects,id',
                Rule::unique('class_subjects')->where(function ($query) use ($request) {
                    return $query->where('class_id', $request->class_id);
                }),
            ],
            'status' => 'required|in:active,inactive',
        ]);

        try {
            ClassSubject::create($request->only('class_id','subject_id','status'));
            return response()->json(['status'=>true, 'message'=>'Class Subject Added Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Class Subject Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ClassSubject $classSubject)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ClassSubject $classSubject)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ClassSubject $classSubject)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'subject_id' => [
                'required',
                'exists:subjects,id',
                Rule::unique('class_subjects')->where(function ($query) use ($request) {
                    return $query->where('class_id', $request->class_id);
                })->ignore($classSubject->id),
            ],
            'status' => 'required|in:active,inactive',
        ]);

        try {
            ClassSubject::where('id',$classSubject->id)->update([
                'class_id' => $request->class_id,
                'subject_id' => $request->subject_id,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Class Subject Updated Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Class Subject Update Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ClassSubject $classSubject)
    {
        try {
            $classSubject->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Class Subject Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Class Subject Delete Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }
}
