<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SessionMaster;
use App\Models\SchoolClass;
use App\Models\Section;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\Period;
use App\Models\Student;

class DropdownController extends Controller
{
    public function sessionMasters()
    {
        $sessionMasters = SessionMaster::where('status','active')->pluck('name','id');
        $view = view('admin.dropdown.session-masters', compact('sessionMasters'))->render();
        return response()->json(['html' => $view]);
    }

    public function classes()
    {
        $classes = SchoolClass::where('status','active')->pluck('name','id');
        $view = view('admin.dropdown.classes', compact('classes'))->render();
        return response()->json(['html' => $view]);
    }

    // Get sections based on class
    public function sections(Request $request)
    {
        $sections = Section::where('class_id',$request->class_id)
            ->where('status','active')
            ->pluck('name','id');
        $view = view('admin.dropdown.sections', compact('sections'))->render();
        return response()->json(['html' => $view]);
    }

    public function subjects()
    {
        $subjects = Subject::select('id','name','code')
            ->get();
        $view = view('admin.dropdown.subjects', compact('subjects'))->render();
        return response()->json(['html' => $view]);
    }

    public function teachers()
    {
        $teachers = Teacher::with('user:id,name')->select('user_id','id','emp_code')
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })->get();
        $view = view('admin.dropdown.teachers', compact('teachers'))->render();
        return response()->json(['html' => $view]);
    }

    public function periods()
    {
        $periods = Period::where('status','active')->pluck('name','id');
        $view = view('admin.dropdown.periods', compact('periods'))->render();
        return response()->json(['html' => $view]);
    }

    public function students()
    {
        $students = Student::with('user:id,name,email')
            ->whereHas('user', function ($query) {
                $query->where('status', 'active');
            })->get();
        $view = view('admin.dropdown.students', compact('students'))->render();
        return response()->json(['html' => $view]);
    }
}
