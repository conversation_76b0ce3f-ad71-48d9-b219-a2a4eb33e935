<?php

namespace App\Http\Controllers;

use App\Models\Period;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class PeriodController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = Period::latest();
        if(isset($request->name)){
            $records = $records->where('name','LIKE', "%$request->name%");
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.periods.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.periods.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    { 
        $request->validate([
            'name' => 'required|max:255|string|unique:periods,name',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            Period::create([
                'name' => $request->name,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Period Added Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Period Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Period $period)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Period $period)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Period $period)
    {
        $request->validate([
            'name' => 'required|max:255|string|unique:periods,name,'.$period->id,
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            Period::where('id',$period->id)->update([
                'name' => $request->name,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Period Updated Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Period Update Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Period $period)
    {
        try {
            $period->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Period Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Period Delete Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }
}
