<?php

namespace App\Http\Controllers;

use App\Models\SessionMaster;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class SessionMasterController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = SessionMaster::latest();
        if(isset($request->name)){
            $records = $records->where('name','LIKE', "%$request->name%");
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.session-masters.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.session-masters.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:session_masters,name',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            SessionMaster::create($request->only('name', 'start_date', 'end_date', 'status'));
            return response()->json(['status'=>true, 'message'=>'Session Master Added Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Session Master Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>'Internal Server Error'],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(SessionMaster $sessionMaster)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SessionMaster $sessionMaster)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SessionMaster $sessionMaster)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:session_masters,name,'.$sessionMaster->id,
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            SessionMaster::where('id',$sessionMaster->id)->update($request->only('name', 'start_date', 'end_date', 'status'));
            return response()->json(['status'=>true, 'message'=>'Session Master Updated Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Session Master Update Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>'Internal Server Error'],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SessionMaster $sessionMaster)
    {
        //
    }
}
