<?php

namespace App\Http\Controllers;

use App\Models\StudentAcademicSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class StudentAcademicSessionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = StudentAcademicSession::with('student:id,user_id','student.user:id,name,email','class:id,name','section:id,name','sessionMaster:id,name')
            ->where('session_master_id',session('session_master_id'));
            if(isset($request->student_name)){
                $records = $records->whereHas('student.user', function ($query) use ($request) {
                    $query->where('name', 'LIKE', '%' . $request->student_name . '%');
                });
            }
            if(isset($request->class_name)){
                $records = $records->whereHas('class', function ($query) use ($request) {
                    $query->where('name', 'LIKE', '%' . $request->class_name . '%');
                });
            }
            if(isset($request->section_name)){
                $records = $records->whereHas('section', function ($query) use ($request) {
                    $query->where('name', 'LIKE', '%' . $request->section_name . '%');
                });
            }
            if(isset($request->status)){
                $records = $records->where('status',$request->status);
            }
            $records = $records->latest();
            $records = $records->paginate(config('constant.paginate'));    
        if ($request->ajax()) {
            $view = view('admin.student-academic-sessions.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }    
        return view('admin.student-academic-sessions.index', compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
            'class_id' => 'required|exists:classes,id',
            'section_id' => 'nullable|exists:sections,id',
            'roll_no' => 'required|string|max:10|unique:student_academic_sessions,roll_no,NULL,id,session_master_id,'.session('session_master_id'),
            'admission_date' => 'required|date',
            'status' => 'required|in:active,inactive',
        ]);

        try{
            $exist = StudentAcademicSession::where('student_id',$request->student_id)
                ->where('class_id',$request->class_id)
                ->where('session_master_id',session('session_master_id'))
                ->first();
            if($exist){
                return response()->json(['status'=>false, 'message'=>'Student already exists in this class!'],400);
            }

            StudentAcademicSession::create([
                'session_master_id' => session('session_master_id'),
                'student_id' => $request->student_id,
                'class_id' => $request->class_id,
                'section_id' => $request->section_id,
                'roll_no' => $request->roll_no,
                'admission_date' => $request->admission_date, 
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Student Academic Session Added Successfully!'],200);   
        }catch(\Exception $ex){
            Log::error('Student Academic Session Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
        
    }

    /**
     * Display the specified resource.
     */
    public function show(StudentAcademicSession $studentAcademicSession)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StudentAcademicSession $studentAcademicSession)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StudentAcademicSession $studentAcademicSession)
    {
        $request->validate([
            'student_id' => 'required|exists:students,id',
            'class_id' => 'required|exists:classes,id',
            'section_id' => 'nullable|exists:sections,id',
            'roll_no' => 'required|string|max:10|unique:student_academic_sessions,roll_no,'.$studentAcademicSession->id.',id,session_master_id,'.session('session_master_id'),
            'admission_date' => 'required|date',
            'status' => 'required|in:active,inactive',
        ]);

        try{
            $exist = StudentAcademicSession::where('student_id',$request->student_id)
                ->where('class_id',$request->class_id)
                ->where('session_master_id',session('session_master_id'))
                ->where('id','!=',$studentAcademicSession->id)
                ->first();
            if($exist){
                return response()->json(['status'=>false, 'message'=>'Student already exists in this class!'],400);
            }

            $studentAcademicSession->update([
                'student_id' => $request->student_id,
                'class_id' => $request->class_id,
                'section_id' => $request->section_id,
                'roll_no' => $request->roll_no,
                'admission_date' => $request->admission_date,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Student Academic Session Updated Successfully!'],200);   
        }catch(\Exception $ex){
            Log::error('Student Academic Session Update Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StudentAcademicSession $studentAcademicSession)
    {
        try {
            $studentAcademicSession->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Student Academic Session Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Student Academic Session Delete Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }
}
