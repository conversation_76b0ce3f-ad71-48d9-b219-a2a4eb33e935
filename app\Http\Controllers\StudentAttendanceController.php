<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StudentAcademicSession;
use App\Models\SchoolClass;
use App\Models\Section;
use App\Models\StudentAttendance;
use Illuminate\Support\Facades\Log;

class StudentAttendanceController extends Controller
{
    public function index(Request $request)
    {
        $classId = $request->class_id ?? 0;
        $sectionId = $request->section_id ?? null;
        $records = StudentAcademicSession::with(
                'student:id,user_id',
                'student.user:id,name,email',
                'student.studentAttendance',
                'student.studentAttendance.markedBy:id,name'
            )
            ->latest()
            ->where('status','active')
            ->where('session_master_id',session('session_master_id'))
            ->orderBy('class_id','asc')
            ->orderBy('section_id','asc')
            ->orderBy('roll_no','asc');
            if(isset($classId)){
                $records = $records->where('class_id',$classId);
            }
            if(isset($sectionId)){
                $records = $records->where('section_id',$sectionId);
            }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.student-attendances.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.student-attendances.index',compact('records','classId','sectionId'));
    }

    public function present(Request $request)
    {
        try{
            $studentAttendance = StudentAttendance::create([
                'student_id' => $request->id,
                'attendance_date' => date('Y-m-d'),
                'status' => 'present',
                'check_in_time' => date('H:i:s'),
                'marked_by' => auth()->id(),
            ]);
            return response()->json(['status'=>true, 'message'=>'Attendance Marked Successfully!','check_in_time'=>$studentAttendance->check_in_time,'marked_by'=>$studentAttendance->markedBy->name]);
        }catch(\Exception $ex){
            Log::error('Student Attendance Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    public function absent(Request $request)
    {
        try{
            $studentAttendance = StudentAttendance::create([
                'student_id' => $request->id,
                'attendance_date' => date('Y-m-d'),
                'status' => 'absent',
                'check_in_time' => date('H:i:s'),
                'marked_by' => auth()->id(),
            ]);
            return response()->json(['status'=>true, 'message'=>'Attendance Marked Successfully!','check_in_time'=>$studentAttendance->check_in_time,'marked_by'=>$studentAttendance->markedBy->name]);
        }catch(\Exception $ex){
            Log::error('Student Attendance Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }
}
