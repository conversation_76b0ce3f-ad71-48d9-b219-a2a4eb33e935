<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;

class StudentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = Student::with('user:id,name,email,address,photo,status')
            ->latest();
        if(isset($request->name)){
            $records = $records->whereHas('user', function ($query) use ($request) {
                $query->where('name','LIKE', "%$request->name%");
            });
        }
        if(isset($request->email)){
            $records = $records->whereHas('user', function ($query) use ($request) {
                $query->where('email','LIKE', "%$request->email%");
            });
        }
        if(isset($request->phone)){
            $records = $records->where('parent_contact_no','Like', "%$request->phone%");
        }
        if(isset($request->status)){
            $records = $records->whereHas('user', function ($query) use ($request) {
                $query->where('status','LIKE', "%$request->status%");
            });
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.students.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.students.index',compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $bloodGroups = config('constant.blood_groups');
        return view('admin.students.create',compact('bloodGroups'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:70',
            'email' => 'required|email|max:70|unique:users',
            'password' => 'required|string|max:20',
            'father_name' => 'required|string|max:70',
            'mother_name' => 'required|string|max:70',
            'father_occupation' => 'nullable|string|max:70',
            'parent_contact_no' => 'required|numeric|digits:10',
            'dob' => 'required|date',
            'gender' => 'required|max:20|string',
            'blood_group' => 'nullable|string|max:10',
            'caste' => 'required|string|max:50',
            'address' => 'required|string|max:255',
            'photo' => 'nullable|mimes:jpg,png,webp,jpeg',
        ]);
        
        try{
            $user = User::create([
                'name' => sanitizeForCSV($request->name),
                'email' => sanitizeForCSV($request->email),
                'password' => Hash::make($request->password),
                'phone' => sanitizeForCSV($request->phone) ?? sanitizeForCSV($request->parent_contact_no),
                'address' => sanitizeForCSV($request->address),
                'status' => sanitizeForCSV($request->status),
            ]);
            $user->roles()->attach(4);
            Student::create([
                'user_id' => $user->id,
                'father_name' => sanitizeForCSV($request->father_name),
                'mother_name' => sanitizeForCSV($request->mother_name),
                'father_occupation' => sanitizeForCSV($request->father_occupation),
                'parent_contact_no' => sanitizeForCSV($request->parent_contact_no),
                'dob' => sanitizeForCSV($request->dob),
                'gender' => sanitizeForCSV($request->gender),
                'blood_group' => sanitizeForCSV($request->blood_group),
                'caste' => sanitizeForCSV($request->caste),
            ]);

            return to_route('students.index')->with('success','Students Added Successfully!');
        } catch (\Exception $ex) {
            Log::error('Student Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return back()->with('error','Internal Server Error!');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Student $student)
    {
        return view('admin.students.show',compact('student'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Student $student)
    {
        $bloodGroups = config('constant.blood_groups');
        return view('admin.students.create',compact('bloodGroups','student'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Student $student)
    {
        $request->validate([
            'name' => 'required|string|max:70',
            'email' => 'required|email|max:70|unique:users,email,'.$student->user_id,
            'father_name' => 'required|string|max:70',
            'mother_name' => 'required|string|max:70',
            'father_occupation' => 'nullable|string|max:70',
            'parent_contact_no' => 'required|numeric|digits:10',
            'dob' => 'required|date',
            'gender' => 'required|max:20|string',
            'blood_group' => 'nullable|string|max:10',
            'caste' => 'required|string|max:50',
            'address' => 'required|string|max:255',
            'photo' => 'nullable|mimes:jpg,png,webp,jpeg',
        ]);
        
        try{
            User::where('id',$student->user_id)->update([
                'name' => sanitizeForCSV($request->name),
                'email' => sanitizeForCSV($request->email),
                'password' => Hash::make($request->password) ?? $student->user->password,
                'phone' => $request->phone ?? $request->parent_contact_no,
                'address' => sanitizeForCSV($request->address),
                'status' => sanitizeForCSV($request->status),
            ]);
            Student::where('id',$student->id)->update([
                'father_name' => sanitizeForCSV($request->father_name),
                'mother_name' => sanitizeForCSV($request->mother_name),
                'father_occupation' => sanitizeForCSV($request->father_occupation),
                'parent_contact_no' => sanitizeForCSV($request->parent_contact_no),
                'dob' => date('Y-m-d', strtotime($request->dob)), //update time not work set mutator
                'gender' => sanitizeForCSV($request->gender),
                'blood_group' => sanitizeForCSV($request->blood_group),
                'caste' => sanitizeForCSV($request->caste),
            ]);

            return to_route('students.index')->with('success','Students Updated Successfully!');
        } catch (\Exception $ex) {
            Log::error('Student Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return back()->with('error','Internal Server Error!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Student $student)
    {
        try {
            $userId = $student->user_id;
            $student->delete();
            User::find($userId)->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Student Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Student Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }
}
