<?php

namespace App\Http\Controllers;

use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;

class SubjectController extends Controller
{
    public function index(Request $request)
    {
        $records = Subject::latest();
        if(isset($request->name)){
            $records = $records->where('name','LIKE', "%$request->name%");
        }
        if(isset($request->code)){
            $records = $records->where('code','LIKE', "%$request->code%");
        }
        if(isset($request->type)){
            $records = $records->where('type',$request->type);
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.subjects.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.subjects.index',compact('records'));
    }

    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255|string',
            'code' => 'required|max:255|string|unique:subjects,code',
            'type' => 'required|in:theory,practical',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            Subject::create([
                'name' => $request->name,
                'code' => $request->code,
                'type' => $request->type,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Subject Added Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Subject Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>'Internal Server Error'],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Subject $subject)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Subject $subject)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Subject $subject)
    {
        $request->validate([
            'name' => 'required|max:255|string',
            'code' => 'required|max:255|string|unique:subjects,code,'.$subject->id,
            'type' => 'required|in:theory,practical',
            'status' => 'required|in:active,inactive'
        ]);

        try {
            Subject::where('id',$subject->id)->update([
                'name' => $request->name,
                'code' => $request->code,
                'type' => $request->type,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Subject Updated Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        try {
            Subject::where('id',$id)->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Subject Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Subject Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
        
    }
}
