<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class TeacherController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = User::whereHas('roles', function ($query) {
            $query->where('slug', 'teacher');
        })->latest()->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.teachers.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.teachers.index',compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|max:20',
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $user = User::create($request->only('name', 'email', 'password', 'phone', 'address', 'status'));
            $user->roles()->attach(3);
            return response()->json(['status'=>true, 'message'=>'Teacher Added Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'email' => 'required|email|unique:users,email,'.$id,
            'phone' => 'required|string|max:20',
            'address' => 'required|string|max:255',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            User::where('id',$id)->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'status' => $request->status,
            ]);
            return response()->json(['status'=>true, 'message'=>'Teacher Updated Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
