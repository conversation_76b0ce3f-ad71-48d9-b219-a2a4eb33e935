<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Teacher;

class TeacherController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = User::with('teacher')
            ->whereHas('roles', function ($query) {
            $query->where('slug', 'teacher');
        })->latest();
        if(isset($request->name)){
            $records = $records->where('name','LIKE', "%$request->name%");
        }
        if(isset($request->phone)){
            $records = $records->where('phone','LIKE', "%$request->phone%");
        }
        if(isset($request->emp_code)){
            $records = $records->whereHas('teacher', function ($query) use ($request) {
                $query->where('emp_code','LIKE', "%$request->emp_code%");
            });
        }
        if(isset($request->joining_date)){
            $records = $records->whereHas('teacher', function ($query) use ($request) {
                $query->where('joining_date',date('Y-m-d', strtotime($request->joining_date)));
            });
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }
        $records = $records->paginate(config('constant.paginate'));

        if ($request->ajax()) {
            $view = view('admin.teachers.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        return view('admin.teachers.index',compact('records'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8|max:20',
            'phone' => 'required|numeric|digits:10',
            'address' => 'required|string|max:255',
            'emp_code' => 'required|string|max:20|unique:teachers,emp_code',
            'qualification' => 'required|string|max:100',
            'experience' => 'required|integer|min:0|max:50',
            'joining_date' => 'required|date',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'phone' => $request->phone,
                'address' => $request->address,
                'status' => $request->status,
            ]);
            $user->roles()->attach(3);
            Teacher::create([
                'user_id' => $user->id,
                'emp_code' => $request->emp_code,
                'qualification' => $request->qualification,
                'experience' => $request->experience,
                'joining_date' => $request->joining_date,
            ]);
            return response()->json(['status'=>true, 'message'=>'Teacher Added Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(String $id)
    {
        $teacher = Teacher::with('user')->findOrFail($id);
        return view('admin.teachers.show', compact('teacher'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'email' => 'required|email|unique:users,email,'.$id,
            'phone' => 'required|numeric|digits:10',
            'address' => 'required|string|max:255',
            'emp_code' => 'required|string|max:20|unique:teachers,emp_code,'.$request->teacher_id,
            'qualification' => 'required|string|max:100',
            'experience' => 'required|integer|min:0|max:50',
            'joining_date' => 'required|date',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            User::where('id',$id)->update([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'status' => $request->status,
            ]);
            Teacher::where('user_id',$id)->where('id',$request->teacher_id)->update([
                'emp_code' => $request->emp_code,
                'qualification' => $request->qualification,
                'experience' => $request->experience,
                'joining_date' => date('Y-m-d', strtotime($request->joining_date)), //update time not work set mutator
            ]);
            return response()->json(['status'=>true, 'message'=>'Teacher Updated Successfully!'],200);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            User::where('id',$id)->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Teacher Deleted Successfully!']);
        } catch (\Exception $ex) {
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!', 'error'=>$ex->getMessage()],500);
        }
    }
}
