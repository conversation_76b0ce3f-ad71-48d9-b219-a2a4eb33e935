<?php

namespace App\Http\Controllers;

use App\Models\TeacherSubject;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\Teacher;
use App\Models\Section;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class TeacherSubjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = TeacherSubject::with(
            'teacher:id,user_id,emp_code',
            'teacher.user:id,name',
            'subject:id,name,code',
            'class:id,name',
            'section:id,name' 
        )->latest();
        if(isset($request->class_name)){
            $records = $records->whereHas('class', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->class_name . '%');
            });
        }
        if(isset($request->subject_name)){
            $records = $records->whereHas('subject', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->subject_name . '%');
            });
        }
        if(isset($request->teacher_name)){
            $records = $records->whereHas('teacher.user', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->teacher_name . '%');
            });
        }
        if(isset($request->section_name)){
            $records = $records->whereHas('section', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->section_name . '%');
            });
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }

        $records = $records->paginate(config('constant.paginate'));

        if ($request->ajax()) {
            $view = view('admin.teacher-subjects.index-data', compact('records'))->render();
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }

        $classes = SchoolClass::pluck('name','id');
        // $subjects = Subject::pluck('name','id');
        // $teachers = Teacher::with('user:id,name')->select('user_id','id')->get();
        // $sections = Section::pluck('name','id');
        return view('admin.teacher-subjects.index',compact('records', 'classes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'subject_id' => 'required|exists:subjects,id',
            'teacher_id' => 'required|exists:teachers,id',
            'section_id' => 'required|exists:sections,id',
            'status' => 'required|in:active,inactive',
        ]);

        $exist = TeacherSubject::where('teacher_id',$request->teacher_id)
            ->where('subject_id',$request->subject_id)
            ->where('class_id',$request->class_id)
            ->where('section_id',$request->section_id)
            ->first();
        if($exist){
            return response()->json(['status'=>false, 'message'=>'Teacher Subject Already Exists!'],400);
        }

        try {
            TeacherSubject::create($request->only('class_id','subject_id','teacher_id','section_id','status'));
            return response()->json(['status'=>true, 'message'=>'Teacher Subject Added Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Teacher Subject Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(TeacherSubject $teacherSubject)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TeacherSubject $teacherSubject)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TeacherSubject $teacherSubject)
    { 
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'subject_id' => 'required|exists:subjects,id',
            'teacher_id' => 'required|exists:teachers,id',
            'section_id' => 'required|exists:sections,id',
            'status' => 'required|in:active,inactive',
        ]);

        $exist = TeacherSubject::where('teacher_id',$request->teacher_id)
            ->where('subject_id',$request->subject_id)
            ->where('class_id',$request->class_id)
            ->where('section_id',$request->section_id)
            ->where('id','!=',$teacherSubject->id)
            ->first();
        if($exist){
            return response()->json(['status'=>false, 'message'=>'Teacher Subject Already Exists!'],400);
        }
        // dd($teacherSubject);
        try {
            TeacherSubject::find($teacherSubject->id)
                ->update([
                    'class_id' => $request->class_id,
                    'subject_id' => $request->subject_id,
                    'teacher_id' => $request->teacher_id,
                    'section_id' => $request->section_id,
                    'status' => $request->status,
                ]);
            return response()->json(['status'=>true, 'message'=>'Teacher Subject Updated Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Teacher Subject Update Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }   
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TeacherSubject $teacherSubject)
    {
        try {
            $teacherSubject->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Teacher Subject Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Teacher Subject Delete Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    public function getSections(Request $request)
    {
        $sections = Section::where('class_id',$request->class_id)->pluck('name','id');
        $view = view('admin.teacher-subjects.section-data', compact('sections'))->render();
        return response()->json(['html' => $view]);
    }
}