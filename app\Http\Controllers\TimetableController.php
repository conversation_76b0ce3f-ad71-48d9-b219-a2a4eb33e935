<?php

namespace App\Http\Controllers;

use App\Models\Timetable;
use App\Models\SchoolClass;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class TimetableController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $records = Timetable::latest();
        if(isset($request->class_name)){
            $records = $records->whereHas('class', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->class_name . '%');
            });
        }
        if(isset($request->subject_name)){
            $records = $records->whereHas('subject', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->subject_name . '%');
            });
        }
        if(isset($request->day)){
            $records = $records->where('day',$request->day);
        }
        if(isset($request->period_name)){
            $records = $records->whereHas('period', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->period_name . '%');
            });
        }
        if(isset($request->teacher_name)){
            $records = $records->whereHas('teacher.user', function ($query) use ($request) {
                $query->where('name', 'LIKE', '%' . $request->teacher_name . '%');
            });
        }
        if(isset($request->status)){
            $records = $records->where('status',$request->status);
        }
        $records = $records->paginate(config('constant.paginate'));
        if ($request->ajax()) {
            $view = view('admin.timetables.index-data', compact('records'))->render();  
            $pagination = view('pagination.links', compact('records'))->render();
            return response()->json([
                'html'       => $view,
                'pagination' => $pagination,
            ]);
        }
        $days = config('constant.weekdays');
        $classes = SchoolClass::pluck('name','id');
        return view('admin.timetables.index', compact('records','days','classes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validate = $request->validate([
            'class_id' => 'required|exists:classes,id',
            'subject_id' => 'required|exists:subjects,id',
            'day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'period_id' => 'required|exists:periods,id',
            'teacher_id' => 'required|exists:teachers,id',
            'section_id' => 'nullable|exists:sections,id',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $existSlot = Timetable::where('class_id',$request->class_id)
                ->where('day',$request->day)
                ->where('period_id',$request->period_id)
                ->where('section_id',$request->section_id)
                ->first();
            if($existSlot){
                return response()->json(['status'=>false, 'message'=>'This class and section already has a subject scheduled for this period!'],400);
            }

            $existTeacher = Timetable::where('teacher_id', $request->teacher_id)
                ->where('day', $request->day)
                ->where('period_id', $request->period_id)
                ->where('status', 'active')
                ->first();
            if ($existTeacher) {
                return response()->json(['status' => false, 'message' => 'Teacher is already assigned during this period on the selected day!'], 400);
            }

            Timetable::create($request->only('class_id','subject_id','day','period_id','teacher_id','section_id','status'));
            return response()->json(['status'=>true, 'message'=>'Timetable Added Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Timetable Store Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!','error'=>'Internal Server Error'],500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Timetable $timetable)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Timetable $timetable)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Timetable $timetable)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'subject_id' => 'required|exists:subjects,id',
            'day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'period_id' => 'required|exists:periods,id',    
            'teacher_id' => 'required|exists:teachers,id',
            'section_id' => 'nullable|exists:sections,id',
            'status' => 'required|in:active,inactive',
        ]);

        try {
            $existSlot = Timetable::where('class_id',$request->class_id)
                ->where('day',$request->day)
                ->where('period_id',$request->period_id)
                ->where('section_id',$request->section_id)
                ->where('id','!=',$timetable->id)
                ->first();
            if($existSlot){
                return response()->json(['status'=>false, 'message'=>'This class and section already has a subject scheduled for this period!'],400);
            }

            $existTeacher = Timetable::where('teacher_id', $request->teacher_id)
                ->where('day', $request->day)
                ->where('period_id', $request->period_id)
                ->where('status', 'active')
                ->where('id','!=',$timetable->id)
                ->first();
            if ($existTeacher) {
                return response()->json(['status' => false, 'message' => 'Teacher is already assigned during this period on the selected day!'], 400);
            }

            Timetable::where('id',$timetable->id)->update($request->only('class_id','subject_id','day','period_id','teacher_id','section_id','status'));
            return response()->json(['status'=>true, 'message'=>'Timetable Updated Successfully!'],200);
        } catch (\Exception $ex) {
            Log::error('Timetable Update Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Timetable $timetable)
    {
        try {
            $timetable->delete();
            return response(['status'=>true, 'code'=>200,'message'=>'Timetable Deleted Successfully!']);
        } catch (\Exception $ex) {
            Log::error('Timetable Delete Error: ', [
                'exception' => $ex->getMessage(),
                'method' => __METHOD__,
                'line' => __LINE__,
            ]);
            return response()->json(['status'=>false, 'message'=>'Internal Server Error!'],500);
        }
    }
}