<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AuditLog extends Model
{
    protected $fillable = [
        'user_id',
        'module',
        'action',
        'description',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
    ];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function getCreatedAtAttribute($value){
        return date('d-m-Y H:i A', strtotime($value));
    }

    public function getUpdatedAtAttribute($value){
        return date('d-m-Y H:i A', strtotime($value));
    }
}
