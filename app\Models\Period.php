<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Period extends Model
{
    protected $fillable = ['name','start_time','end_time','status'];

    protected function getStartTimeFormatAttribute()
    {
        return date('h:i A', strtotime($this->start_time));
    }

    protected function getEndTimeFormatAttribute()
    {
        return date('h:i A', strtotime($this->end_time));
    }
}
