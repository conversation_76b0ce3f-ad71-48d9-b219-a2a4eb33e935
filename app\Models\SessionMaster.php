<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SessionMaster extends Model
{
    protected $fillable = ['name','start_date','end_date','status'];

    protected function getStartDateFormatAttribute()
    {
        return date('d-m-Y', strtotime($this->start_date));
    }

    protected function getEndDateFormatAttribute()
    {
        return date('d-m-Y', strtotime($this->end_date));
    }
}
