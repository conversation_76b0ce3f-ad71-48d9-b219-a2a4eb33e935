<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = ['session_master_id','user_id','class_id','section_id','father_name','mother_name','father_occupation','parent_contact_no','roll_no','dob','gender','blood_group','admission_date','caste'];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function studentAttendance(){
        return $this->hasOne(StudentAttendance::class);
    }

    public function academicSession(){
        return $this->hasMany(StudentAcademicSession::class);
    }

    public function getAdmissionDateAttribute($value){
        return date('d-m-Y', strtotime($value));
    }


    public function setDobAttribute($value)
    {
        $this->attributes['dob'] = date('Y-m-d', strtotime($value));
    }

    public function getDobAttribute($value){
        return date('d-m-Y', strtotime($value));
    }
}
