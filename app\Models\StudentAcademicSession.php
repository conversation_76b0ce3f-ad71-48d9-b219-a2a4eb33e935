<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentAcademicSession extends Model
{
    protected $fillable = ['session_master_id','student_id','class_id','section_id','roll_no','admission_date','status'];

    public function student(){
        return $this->belongsTo(Student::class);
    }

    public function sessionMaster(){
        return $this->belongsTo(SessionMaster::class);
    }

    public function class(){
        return $this->belongsTo(SchoolClass::class);
    }

    public function section(){
        return $this->belongsTo(Section::class);
    }

    public function getAdmissionDateAttribute($value){
        return date('d-m-Y', strtotime($value));
    }
}
