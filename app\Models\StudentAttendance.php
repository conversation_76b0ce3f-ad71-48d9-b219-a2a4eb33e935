<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StudentAttendance extends Model
{
    protected $fillable = ['student_id','attendance_date','status','check_in_time','remarks','marked_by'];

    public function student(){
        return $this->belongsTo(Student::class);
    }

    public function markedBy(){
        return $this->belongsTo(User::class, 'marked_by');
    }

    public function getCheckInTimeAttribute($value){
        return date('h:i A', strtotime($value));
    }
}
