<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Teacher extends Model
{
    protected $fillable = ['user_id','emp_code','qualification','experience','joining_date'];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function subjects(){
        return $this->belongsToMany(Subject::class);
    }

    protected function joiningDate(): Attribute
    {
        return Attribute::make(
            set: fn ($value) => date('Y-m-d', strtotime($value)), //update time not work
            get: fn ($value) => date('d-m-Y', strtotime($value)),
        );
    }

}
