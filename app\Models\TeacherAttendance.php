<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherAttendance extends Model
{
    protected $fillable = ['teacher_id','attendance_date','status','check_in_time','check_out_time','working_hours','remarks','leave_type','marked_by'];

    public function teacher(){
        return $this->belongsTo(Teacher::class);
    }

    public function markedBy(){
        return $this->belongsTo(User::class);
    }
}
