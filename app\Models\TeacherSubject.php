<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TeacherSubject extends Model
{
    protected $fillable = ['session_master_id', 'teacher_id','subject_id','class_id','section_id','status'];

    public function sessionMaster(){
        return $this->belongsTo(SessionMaster::class);
    }

    public function teacher(){
        return $this->belongsTo(Teacher::class);
    }

    public function subject(){
        return $this->belongsTo(Subject::class);
    }

    public function class(){
        return $this->belongsTo(SchoolClass::class);
    }

    public function section(){
        return $this->belongsTo(Section::class);
    }
}
