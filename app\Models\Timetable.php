<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Timetable extends Model
{
    protected $fillable = ['class_id','section_id','day','period_id','subject_id','teacher_id','status'];

    public function class(){
        return $this->belongsTo(SchoolClass::class);
    }

    public function section(){
        return $this->belongsTo(Section::class);
    }

    public function period(){
        return $this->belongsTo(Period::class);
    }

    public function subject(){
        return $this->belongsTo(Subject::class);
    }

    public function teacher(){
        return $this->belongsTo(Teacher::class);
    }
}
