<?php

namespace App\Observers;

use App\Models\TeacherSubject;

class TeacherSubjectObserver
{
    public function creating(TeacherSubject $teacherSubject): void
    {
        $teacherSubject->session_master_id = session('session_master_id');
    }

    /**
     * Handle the TeacherSubject "created" event.
     */
    public function created(TeacherSubject $teacherSubject): void
    {
        auditLog('TeacherSubject', 'create', 'Teacher Subject Created', null, $teacherSubject->toArray());
    }

    /**
     * Handle the TeacherSubject "updated" event.
     */
    public function updated(TeacherSubject $teacherSubject): void
    {
        auditLog('TeacherSubject', 'update', 'Teacher Subject Updated', $teacherSubject->getOriginal(), $teacherSubject->getChanges());
    }

    /**
     * Handle the TeacherSubject "deleted" event.
     */
    public function deleted(TeacherSubject $teacherSubject): void
    {
        auditLog('TeacherSubject', 'delete', 'Teacher Subject Deleted', $teacherSubject->toArray(), null);
    }

    /**
     * Handle the TeacherSubject "restored" event.
     */
    public function restored(TeacherSubject $teacherSubject): void
    {
        auditLog('TeacherSubject', 'restore', 'Teacher Subject Restored', null, json_encode($teacherSubject));
    }

    /**
     * Handle the TeacherSubject "force deleted" event.
     */
    public function forceDeleted(TeacherSubject $teacherSubject): void
    {
        auditLog('TeacherSubject', 'forceDelete', 'Teacher Subject Force Deleted', null, json_encode($teacherSubject));
    }
}
