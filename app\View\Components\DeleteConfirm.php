<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class DeleteConfirm extends Component
{
    /**
     * Create a new component instance.
     */
    public $functionName;
    public $deleteUrl;
    public function __construct($functionName, $deleteUrl)
    {
        $this->functionName = $functionName;
        $this->deleteUrl = $deleteUrl;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.delete-confirm');
    }
}
