<?php
use App\Models\AuditLog;

if(!function_exists('auditLog')){
    function auditLog($module, $action, $description, $oldValues = null, $newValues = null){
        AuditLog::create([
            'user_id' => auth()->id(),
            'module' => $module,
            'action' => $action,
            'description' => $description,
            'old_values'  => is_array($oldValues) ? json_encode($oldValues) : $oldValues,
            'new_values'  => is_array($newValues) ? json_encode($newValues) : $newValues,
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('User-Agent'),
        ]);
    }
}

if (!function_exists("sanitizeForCSV")) {
    function sanitizeForCSV($data) {
        // Encode special HTML characters
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');

        // Escape leading characters that may trigger formulas in spreadsheet software
        $specialChars = ['=', '+', '-', '@']; 
        if (in_array(substr($data, 0, 1), $specialChars)) {
            // Prefix with a single quote to prevent formula interpretation
            $data = "'" . $data;
        }

        return $data;
    }
}