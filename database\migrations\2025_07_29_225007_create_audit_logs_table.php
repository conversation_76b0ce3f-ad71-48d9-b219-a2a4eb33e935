<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who did the action
            $table->string('module', 100); // Example: 'Student', 'Fee', 'Attendance'
            $table->string('action', 50); // Example: 'create', 'update', 'delete'
            $table->text('description')->nullable(); // Short description
            $table->json('old_values')->nullable(); // Before change
            $table->json('new_values')->nullable(); // After change
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable(); // Browser/Device info
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
