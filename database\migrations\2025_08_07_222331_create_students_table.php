<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');
            $table->string('father_name',70);
            $table->string('mother_name',70);
            $table->string('father_occupation',70)->nullable();
            $table->string('parent_contact_no',10)->nullable();
            $table->date('dob');
            $table->string('gender',10);
            $table->string('blood_group',10)->nullable();
            $table->string('caste',50);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
