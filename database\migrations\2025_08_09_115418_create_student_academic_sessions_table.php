<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_academic_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained('students')->onDelete('cascade');
            $table->foreignId('session_master_id')->constrained('session_masters')->onDelete('cascade');
            $table->foreignId('class_id')->constrained('classes')->onDelete('cascade');
            $table->foreignId('section_id')->nullable()->constrained('sections')->onDelete('cascade');
            $table->string('roll_no',10);
            $table->date('admission_date');
            $table->enum('status',['active','inactive']);
            $table->timestamps();

            $table->unique(['student_id', 'class_id', 'session_master_id'], 'unique_student_class_session');
            $table->unique(['roll_no', 'class_id', 'session_master_id'],'unique_roll_no_class_session');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_academic_sessions');
    }
};
