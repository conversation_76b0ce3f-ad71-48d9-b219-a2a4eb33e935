<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teacher_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('teacher_id')->constrained()->onDelete('cascade');
            $table->date('attendance_date');
            $table->enum('status', ['present', 'absent', 'late', 'half_day', 'on_leave'])->default('present');
            $table->time('check_in_time')->nullable();
            $table->time('check_out_time')->nullable();
            $table->decimal('working_hours', 4, 2)->nullable(); // Total working hours
            $table->text('remarks')->nullable();
            $table->enum('leave_type', ['sick', 'casual', 'earned', 'maternity', 'other'])->nullable();
            $table->foreignId('marked_by')->nullable()->constrained('users')->onDelete('set null'); // Admin who marked
            $table->timestamps();

            // Ensure one attendance record per teacher per date
            $table->unique(['teacher_id', 'attendance_date']);
            
            // Index for faster queries
            $table->index(['attendance_date']);
            $table->index(['teacher_id', 'attendance_date']);
            $table->index(['status', 'attendance_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teacher_attendances');
    }
};
