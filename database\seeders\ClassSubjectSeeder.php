<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ClassSubject;

class ClassSubjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(ClassSubject::count() > 0){
            return;
        }

        $classSubjects = [
            // Class 6
            ['class_id' => 9, 'subject_id' => 1],
            ['class_id' => 9, 'subject_id' => 2],
            ['class_id' => 9, 'subject_id' => 3],
            ['class_id' => 9, 'subject_id' => 4],
            ['class_id' => 9, 'subject_id' => 5],
            ['class_id' => 9, 'subject_id' => 6],
            ['class_id' => 9, 'subject_id' => 7],

            // Class 7
            ['class_id' => 10, 'subject_id' => 1],
            ['class_id' => 10, 'subject_id' => 2],
            ['class_id' => 10, 'subject_id' => 3],
            ['class_id' => 10, 'subject_id' => 4],
            ['class_id' => 10, 'subject_id' => 5],
            ['class_id' => 10, 'subject_id' => 6],
            ['class_id' => 10, 'subject_id' => 7],

            // Class 8
            ['class_id' => 11, 'subject_id' => 1],
            ['class_id' => 11, 'subject_id' => 2],
            ['class_id' => 11, 'subject_id' => 3],
            ['class_id' => 11, 'subject_id' => 4],
            ['class_id' => 11, 'subject_id' => 5],
            ['class_id' => 11, 'subject_id' => 6],
            ['class_id' => 11, 'subject_id' => 7],

            // Class 9
            ['class_id' => 12, 'subject_id' => 1],
            ['class_id' => 12, 'subject_id' => 2],
            ['class_id' => 12, 'subject_id' => 3],
            ['class_id' => 12, 'subject_id' => 4],
            ['class_id' => 12, 'subject_id' => 5],
            ['class_id' => 12, 'subject_id' => 6],
            ['class_id' => 12, 'subject_id' => 7],

            // Class 10
            ['class_id' => 13, 'subject_id' => 1],
            ['class_id' => 13, 'subject_id' => 2],
            ['class_id' => 13, 'subject_id' => 3],  
            ['class_id' => 13, 'subject_id' => 4],  
            ['class_id' => 13, 'subject_id' => 5],  
            ['class_id' => 13, 'subject_id' => 6],  
            ['class_id' => 13, 'subject_id' => 7],
        ];

        foreach ($classSubjects as $classSubject) {
            ClassSubject::Create($classSubject);
        }
    }
}
