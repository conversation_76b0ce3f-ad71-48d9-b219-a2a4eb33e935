<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            \Database\Seeders\UserSeeder::class,
            \Database\Seeders\RoleSeeder::class,
            \Database\Seeders\RoleUserSeeder::class,
          //  \Database\Seeders\PermissionSeeder::class,
         //   \Database\Seeders\PermissionRoleSeeder::class,
            \Database\Seeders\TeacherSeeder::class,
           \Database\Seeders\TeacherSubjectSeeder::class,
            \Database\Seeders\SchoolClassSeeder::class,
            \Database\Seeders\SectionSeeder::class,
            \Database\Seeders\PeriodSeeder::class,
            \Database\Seeders\SubjectSeeder::class,
            \Database\Seeders\ClassSubjectSeeder::class,
        ]);
    }
}