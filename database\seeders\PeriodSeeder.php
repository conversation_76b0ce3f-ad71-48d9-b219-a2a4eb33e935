<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Period;

class PeriodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(Period::count() > 0){
            return;
        }

        $periods = [
            ['name' => 'Morning Assembly', 'start_time' => '08:00:00', 'end_time' => '08:15:00'],
            ['name' => 'Period 1', 'start_time' => '08:20:00', 'end_time' => '09:05:00'],
            ['name' => 'Period 2', 'start_time' => '09:10:00', 'end_time' => '09:55:00'],
            ['name' => 'Period 3', 'start_time' => '10:00:00', 'end_time' => '10:45:00'],
            ['name' => 'Short Break', 'start_time' => '10:45:00', 'end_time' => '11:00:00'],
            ['name' => 'Period 4', 'start_time' => '11:00:00', 'end_time' => '11:45:00'],
            ['name' => 'Period 5', 'start_time' => '11:50:00', 'end_time' => '12:35:00'],
            ['name' => 'Lunch Break', 'start_time' => '12:35:00', 'end_time' => '01:10:00'],
            ['name' => 'Period 6', 'start_time' => '01:10:00', 'end_time' => '01:55:00'],
            ['name' => 'Period 7', 'start_time' => '02:00:00', 'end_time' => '02:45:00'],
        ];

        foreach ($periods as $period) {
            Period::Create($period);
        }
    }
}
