<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SchoolClass;

class SchoolClassSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(SchoolClass::count() > 0){
            return;
        }

        $classes = [
            ['name' => 'Nursery'],
            ['name' => 'LKG'],
            ['name' => 'UKG'],
            ['name' => '1st'],
            ['name' => '2nd'],
            ['name' => '3rd'],
            ['name' => '4th'],
            ['name' => '5th'],
            ['name' => '6th'],
            ['name' => '7th'],
            ['name' => '8th'],
            ['name' => '9th'],
            ['name' => '10th'],
            ['name' => '11th'],
            ['name' => '12th'],
        ];

        foreach ($classes as $class) {
            SchoolClass::Create($class);
        }
    }
}
