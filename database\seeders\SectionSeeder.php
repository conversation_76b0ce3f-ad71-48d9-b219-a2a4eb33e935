<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Section;

class SectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(Section::count() > 0){
            return;
        }

        $sections = [
            ['class_id' => 9, 'name' => 'A'],
            ['class_id' => 9, 'name' => 'B'],
            ['class_id' => 9, 'name' => 'C'],
            ['class_id' => 9, 'name' => 'D'],
            ['class_id' => 10, 'name' => 'A'],
            ['class_id' => 10, 'name' => 'B'],
            ['class_id' => 10, 'name' => 'C'],
            ['class_id' => 10, 'name' => 'D'],
        ];

        foreach ($sections as $section) {
            Section::Create($section);
        }
    }
}
