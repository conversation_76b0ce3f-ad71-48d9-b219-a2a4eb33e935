<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Subject;

class SubjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(Subject::count() > 0){
            return;
        }

        $subjects = [
            ['name' => 'Mathematics', 'code' => 'MATH', 'type' => 'theory'],
            ['name' => 'Science', 'code' => 'SCI', 'type' => 'theory'],
            ['name' => 'English', 'code' => 'ENG', 'type' => 'theory'],
            ['name' => 'Hindi', 'code' => 'HIN', 'type' => 'theory'],
            ['name' => 'Sanskrit', 'code' => 'SAN', 'type' => 'theory'],
            ['name' => 'Social Science', 'code' => 'SS', 'type' => 'theory'],
            ['name' => 'Computer Science', 'code' => 'CSC', 'type' => 'theory'],
        ];

        foreach ($subjects as $subject) {
            Subject::Create($subject);
        }
    }
}
