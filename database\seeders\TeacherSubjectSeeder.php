<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TeacherSubject;

class TeacherSubjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if(TeacherSubject::count() > 0){
            return;
        }

        $teacherSubjects = [
            ['session_master_id' => 1, 'teacher_id' => 1, 'subject_id' => 1, 'class_id' => 9, 'section_id' => 1],
            ['session_master_id' => 1, 'teacher_id' => 1, 'subject_id' => 2, 'class_id' => 9, 'section_id' => 2],
            ['session_master_id' => 1, 'teacher_id' => 1, 'subject_id' => 3, 'class_id' => 9, 'section_id' => 4],
            ['session_master_id' => 1, 'teacher_id' => 1, 'subject_id' => 1, 'class_id' => 10, 'section_id' => 6],
            ['session_master_id' => 1, 'teacher_id' => 1, 'subject_id' => 2, 'class_id' => 10, 'section_id' => 7],
            ['session_master_id' => 1, 'teacher_id' => 1, 'subject_id' => 3, 'class_id' => 10, 'section_id' => 8],
        ];

        foreach ($teacherSubjects as $teacherSubject) {
            TeacherSubject::Create($teacherSubject);
        }
    }
}
