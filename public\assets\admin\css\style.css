/* Match border, height, and padding for single Select2 fields */
.select2-container--default .select2-selection--single {
    border: 1px solid #ced4da !important; /* same as <PERSON>trap input border */
    height: 38px !important; /* match input height */
    padding: 6px 12px;
    border-radius: 4px; /* optional: match input rounding */
    box-shadow: none !important;
}

/* Text alignment inside the dropdown */
.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px !important;
    padding-left: 0px;
    padding-right: 0px;
}

/* Dropdown arrow alignment */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px !important;
    top: 1px !important;
}

body {
    min-height: 100vh;
    overflow-x: hidden;
}

.sidebar {
    min-height: 100vh;
    background-color: #343a40;
    color: white;
    width: 280px;
    overflow-y: auto;
    max-height: 100vh;
    transition: all 0.3s ease;
}

.sidebar a {
    color: #fff;
    text-decoration: none;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.collapsed:hover {
    background-color: #495057;
}

.sidebar .nav-link.active {
    background-color: #007bff;
}

.content {
    padding: 20px;
    transition: margin-left 0.3s ease;
    min-height: calc(100vh - 56px - 60px); /* viewport - navbar - footer */
    overflow-x: auto;
}

/* Responsive table wrapper */
.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Form responsiveness */
.form-control, .form-select {
    font-size: 0.875rem;
}

/* Button responsiveness */
.btn {
    font-size: 0.875rem;
}

/* Card responsiveness */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.sidebar .collapse-inner {
    padding-left: 1.5rem;
}

/* Tablet and Mobile Responsive */
@media (max-width: 1199.98px) {
    .sidebar {
        position: fixed;
        top: 56px; /* Height of navbar */
        left: 0;
        z-index: 1040;
        transform: translateX(-100%);
        width: 260px;
        height: calc(100vh - 56px);
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .content {
        margin-left: 0;
        padding: 15px;
    }

    .overlay {
        display: none;
        position: fixed;
        top: 56px;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1039;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .overlay.show {
        display: block;
        opacity: 1;
    }
}

/* Mobile specific adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        width: 240px;
    }

    .content {
        padding: 10px;
    }
}

/* Extra small devices */
@media (max-width: 575.98px) {
    .sidebar {
        width: 220px;
    }

    .content {
        padding: 8px;
    }

    /* Mobile specific adjustments */
    .navbar-brand {
        font-size: 1rem;
    }

    .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.5rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .card-header {
        padding: 0.5rem 0.75rem;
    }

    .card-body {
        padding: 0.75rem;
    }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .content {
        padding: 12px;
    }

    .btn {
        font-size: 0.85rem;
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .content {
        padding: 15px;
    }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .content {
        padding: 18px;
    }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
    .content {
        margin-left: 280px;
        padding: 20px;
    }

    .sidebar {
        position: fixed;
        top: 56px;
        left: 0;
        transform: translateX(0);
        height: calc(100vh - 56px);
    }
}

.btn-circle {
    width: 30px;
    height: 30px;
    padding: 0;
    border-radius: 50%;
    font-size: 0.875rem;
}