@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td id="user_name{{ $row->id }}">{{ $row->user->name ?? '' }}</td>   
        <td id="module{{ $row->id }}">{{ $row->module ?? '' }}</td>   
        <td id="action{{ $row->id }}">{{ $row->action ?? '' }}</td>   
        <td id="description{{ $row->id }}">{{ Str::limit($row->description ?? '', 50) }}</td>   
        <td id="created_at{{ $row->id }}">{{ $row->created_at }}</td>   
        <td>
            <button class="btn btn-sm btn-outline-info" type="button" title="View Audit Log" onclick="viewAuditLog({{ $row->id }})">
                <i class="fa-solid fa-eye"></i>
                <input type="hidden" id="view_audit_log_old_values_data{{ $row->id }}" value="{{ $row->old_values ?? '' }}">
                <input type="hidden" id="view_audit_log_new_values_data{{ $row->id }}" value="{{ $row->new_values ?? '' }}">
                <input type="hidden" id="view_audit_log_ip_address_data{{ $row->id }}" value="{{ $row->ip_address ?? '' }}">
                <input type="hidden" id="view_audit_log_user_agent_data{{ $row->id }}" value="{{ $row->user_agent ?? '' }}">
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="6" class="text-center">No Audit Logs found.</td>
    </tr>
@endforelse