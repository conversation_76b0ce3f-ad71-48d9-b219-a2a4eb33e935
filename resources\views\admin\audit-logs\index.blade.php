@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Audit Logs')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Audit Logs Management</h4>
        </div>

        <!-- Filter Input -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="module_search" placeholder="Search by Module...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="action_search" placeholder="Search by Action...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="user_name_search" placeholder="Search by User Name...">
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>

        <!-- Audit Logs Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="auditLogsTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>User</th>
                        <th>Module</th>
                        <th>Action</th>
                        <th>Description</th>   
                        <th>Date</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.audit-logs.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- View Audit Log Modal -->
    <div class="modal fade" id="viewAuditLogModal" tabindex="-1" aria-labelledby="viewAuditLogModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="viewAuditLogModalLabel">View Audit Log</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>User</h6>
                            <p id="view_audit_log_user"></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Module</h6>
                            <p id="view_audit_log_module"></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Action</h6>
                            <p id="view_audit_log_action"></p>
                        </div>
                        <div class="col-md-6">
                            <h6>Description</h6>
                            <p id="view_audit_log_description"></p>
                        </div>
                        <div class="col-md-12">
                            <!-- Table for showing comparison -->
                            <table id="viewauditLogTable" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Field</th>
                                        <th>Old Value</th>
                                        <th>New Value</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>IP Address</h6>
                            <p id="view_audit_log_ip_address"></p>
                        </div>
                        <div class="col-md-6">
                            <h6>User Agent</h6>
                            <p id="view_audit_log_user_agent"></p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    <script>
        function search(){
            const module = $('#module_search').val();
            const action = $('#action_search').val();
            const user_name = $('#user_name_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('audit-logs.index') }}",
                data: {
                    'module': module,
                    'action': action,
                    'user_name': user_name
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#module_search').val('');
            $('#action_search').val('');
            $('#user_name_search').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            const url = $(this).attr('href');
            const module = $('#module_search').val();
            const action = $('#action_search').val();
            const user_name = $('#user_name_search').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'module': module,
                    'action': action,
                    'user_name': user_name
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });

        function viewAuditLog(id){
            const user = $('#user_name'+id).text();
            const module = $('#module'+id).text();
            const action = $('#action'+id).text();
            const description = $('#description'+id).text();
            const old_values = JSON.parse($('#view_audit_log_old_values_data'+id).val() || 'null');
            const new_values = JSON.parse($('#view_audit_log_new_values_data'+id).val() || 'null');
            const ip_address = $('#view_audit_log_ip_address_data'+id).val();
            const user_agent = $('#view_audit_log_user_agent_data'+id).val();

            console.log(old_values);
            console.log(new_values);

            $('#view_audit_log_user').text(user);
            $('#view_audit_log_module').text(module);
            $('#view_audit_log_action').text(action);
            $('#view_audit_log_description').text(description);
            
            let tbody = '';

            if (!old_values && new_values) {
                // CREATE
                for (let field in new_values) {
                    tbody += `<tr>
                                <td>${field}</td>
                                <td>-</td>
                                <td>${new_values[field]}</td>
                            </tr>`;
                }
            } 
            else if (old_values && !new_values) {
                // DELETE
                for (let field in old_values) {
                    tbody += `<tr>
                                <td>${field}</td>
                                <td>${old_values[field]}</td>
                                <td>-</td>
                            </tr>`;
                }
            } 
            else if (old_values && new_values) {
                // UPDATE
                for (let field in new_values) {
                    tbody += `<tr>
                                <td>${field}</td>
                                <td>${old_values[field] ?? '-'}</td>
                                <td>${new_values[field] ?? '-'}</td>
                            </tr>`;
                }
            }

            $('#viewauditLogTable tbody').html(tbody);

            $('#view_audit_log_ip_address').text(ip_address);
            $('#view_audit_log_user_agent').text(user_agent);
            $('#viewAuditLogModal').modal('show');
        }
    </script>
@endpush