<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{{ config('constant.app_name') }} - Login</title>
    <link rel="icon" type="image/jpg" href="{{ asset('logo_favicon.jpg') }}">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 420px;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 35px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 20px 20px 0 0;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h4 {
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: #6c757d;
            font-size: 0.95rem;
            margin-bottom: 0;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .form-floating > label {
            color: #6c757d;
            font-weight: 500;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .footer-text {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .footer-text small {
            color: #6c757d;
            font-size: 0.85rem;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .login-container {
                padding: 15px;
            }

            .login-card {
                padding: 30px 25px;
                border-radius: 16px;
            }

            .login-header h4 {
                font-size: 1.3rem;
            }

            .form-control {
                padding: 10px 14px;
            }

            .login-btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 360px) {
            .login-card {
                padding: 25px 20px;
            }
        }

        /* Animation */
        .login-card {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <link rel="stylesheet" href="{{ asset('assets/bootstrap/bootstrap.min.css') }}">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

    {{-- Jquery --}}
    <script src="{{ asset('assets/jquery/jquery.min.js') }}"></script>

    {{-- Alertify JS --}}
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/alertify/alertify-bootstrap-theme.min.css') }}">
    <script src="{{ asset('assets/alertify/alertify.min.js') }}"></script>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h4>{{ config('constant.app_name') }}</h4>
                <p>Welcome back! Please login to your account</p>
                <x-alert />
            </div>

            <form class="login-form" action="{{ route('login.submit') }}" method="POST">
                @csrf

                <div class="form-floating">
                    <input type="email"
                           name="email"
                           class="form-control"
                           id="email"
                           placeholder="<EMAIL>"
                           value="{{ old('email') }}"
                           required>
                    <label for="email">
                        <i class="fas fa-envelope me-2"></i>Email Address
                    </label>
                </div>

                <div class="form-floating">
                    <input type="password"
                           name="password"
                           class="form-control"
                           id="password"
                           placeholder="Password"
                           required>
                    <label for="password">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                </div>

                <div class="form-floating">
                    <select name="session_master_id"
                            id="session_master_id"
                            class="form-select"
                            required>
                        <option value="">Select here ..</option>
                    </select>
                    <label for="session_master_id">
                        <i class="fas fa-calendar me-2"></i>Academic Session
                    </label>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary login-btn">
                        <i class="fas fa-sign-in-alt me-2"></i>Login to Dashboard
                    </button>
                </div>

                <div class="footer-text">
                    <small>&copy; {{ date('Y') }} {{ config('constant.footer_company_name') }}. All rights reserved.</small>
                </div>
            </form>
        </div>
    </div>

    <script src="{{ asset('assets/bootstrap/bootstrap.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            $.ajax({
                type: "GET",
                url: "{{ route('dropdown.session-masters') }}",
                success: function (response) {
                    $('#session_master_id').html(response.html);
                }
            });
        });
    </script>
</body>
</html>
