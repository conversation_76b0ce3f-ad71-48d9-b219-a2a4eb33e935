<!-- Add Class Subject Modal -->
<div class="modal fade" id="addClassSubjectModal" tabindex="-1" aria-labelledby="addClassSubjectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addClassSubjectModalLabel">Add New Class Subject</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                    <select name="class_id" id="class_subject_class_id" class="form-select" required>
                        <option value="">Select here ..</option>
                        @foreach ($classes as $id => $name)
                            <option value="{{ $id }}" @selected(old('class_id') == $id)>{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label for="subject_id" class="form-label">Subject <span class="text-danger">*</span></label>
                    <select name="subject_id" id="class_subject_subject_id" class="form-select" required>
                        <option value="">Select here ..</option>
                        @foreach ($subjects as $id => $name)
                            <option value="{{ $id }}" @selected(old('subject_id') == $id)>{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="class_subject_status" class="form-select" required>
                        <option value="active" @selected(old('status') == "active")>Active</option>
                        <option value="inactive" @selected(old('status') == "inactive")>Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addClassSubject()">Save Class Subject</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addClassSubject(){
        const class_id = $('#class_subject_class_id').val();
        const subject_id = $('#class_subject_subject_id').val();    
        const status = $('#class_subject_status').val();

        if(class_id == '' || subject_id == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('class-subjects.store') }}",
            data: {
                'class_id': class_id,   
                'subject_id': subject_id,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addClassSubjectModal').modal('hide');
                    $('#class_subject_class_id').val('');
                    $('#class_subject_subject_id').val('');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>   