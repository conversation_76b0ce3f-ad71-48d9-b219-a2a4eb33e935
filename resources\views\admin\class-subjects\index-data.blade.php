@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->class->name ?? '' }}</td>
        <td>{{ $row->subject->name ?? '' }}</td>
        <td  class="text-center">{{ $row->created_at->format('d M Y, h:i A') }}</td>
        <td class="text-center">
            @if ($row->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Subject Class" onclick="editClassSubject({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_class_subject_class_id_data{{ $row->id }}" value="{{ $row->class->id ?? '' }}">
                <input type="hidden" id="edit_class_subject_subject_id_data{{ $row->id }}" value="{{ $row->subject->id ?? '' }}">
                <input type="hidden" id="edit_class_subject_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Subject" onclick="deleteClassSubject({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No Class Subjects found.</td>
    </tr>
@endforelse