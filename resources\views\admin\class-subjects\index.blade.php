@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Class Subjects')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Class Subjects Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClassSubjectModal">
                <i class="fas fa-plus"></i> Add Class Subject
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="class_subject_class_name_search" placeholder="Search by Class Name...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="class_subject_subject_name_search" placeholder="Search by Subject Name...">
            </div>
            <div class="col-md-3">
                <select name="status" id="class_subject_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>

        <!-- Class Subjects Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">    
                <thead class="table-dark">
                    <tr>    
                        <th>#</th>
                        <th>Class</th>
                        <th>Subject</th>
                        <th class="text-center">Created At</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.class-subjects.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Edit Section Modal -->
    <div class="modal fade" id="editClassSubjectModal" tabindex="-1" aria-labelledby="editClassSubjectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editClassSubjectModalLabel">Update Class Subject</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_class_subject_id">
                    <div class="mb-3">
                        <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                        <select name="class_id" id="edit_class_subject_class_id" class="form-select" required>
                            <option value="">Select here ..</option>
                            @foreach ($classes as $id => $name)
                                <option value="{{ $id }}">{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="subject_id" class="form-label">Subject <span class="text-danger">*</span></label>
                        <select name="subject_id" id="edit_class_subject_subject_id" class="form-select" required>
                            <option value="">Select here ..</option>
                            @foreach ($subjects as $id => $name)
                                <option value="{{ $id }}">{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_class_subject_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateClassSubject()">Update Section</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            
            $.ajax({
                url: url,
                type: "GET",
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });

        function search(){
            const class_name = $('#class_subject_class_name_search').val();
            const subject_name = $('#class_subject_subject_name_search').val();
            const status = $('#class_subject_status_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('class-subjects.index') }}",
                data: {
                    'class_name': class_name,
                    'subject_name': subject_name,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        function resetSearchForm(){
            $('#class_subject_class_name_search').val('');
            $('#class_subject_subject_name_search').val('');
            $('#class_subject_status_search').val('');
            search();
        }
    </script>

    <script>
        // Edit 
        function editClassSubject(id){
            $('#edit_class_subject_id').val(id);
            $('#edit_class_subject_class_id').val($('#edit_class_subject_class_id_data'+id).val());
            $('#edit_class_subject_subject_id').val($('#edit_class_subject_subject_id_data'+id).val());
            $('#edit_class_subject_status').val($('#edit_class_subject_status_data'+id).val());
            $('#editClassSubjectModal').modal('show');
        }

        // Update
        function updateClassSubject(){
            const id = $('#edit_class_subject_id').val();
            const class_id = $('#edit_class_subject_class_id').val();
            const subject_id = $('#edit_class_subject_subject_id').val();    
            const status = $('#edit_class_subject_status').val();

            if(class_id == '' || subject_id == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('class-subjects.update', ':id') }}".replace(':id', id),
                data: {
                    'class_id': class_id,   
                    'subject_id': subject_id,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editClassSubjectModal').modal('hide');
                        $('#edit_class_subject_class_id').val('');
                        $('#edit_class_subject_subject_id').val('');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        let firstError = Object.values(errors)[0][0]; // first error message

                        alertify.alert()
                            .setting({
                                title: 'Validation Error',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                            }).show();
                    } else {
                        alertify.alert()
                            .setting({
                                title: 'Server Error',
                                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                            }).show();
                    }
                }
            });
        }
    </script>
    <x-delete-confirm functionName="deleteClassSubject" deleteUrl="class-subjects.destroy" />
    @include('admin.class-subjects.add-modal')
@endpush
