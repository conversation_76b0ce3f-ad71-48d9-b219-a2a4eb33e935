error: function (xhr) {
    if (xhr.status === 422) {
        let errors = xhr.responseJSON.errors;
        let firstError = Object.values(errors)[0][0]; // first error message

        alertify.alert()
            .setting({
                title: 'Validation Error',
                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
            }).show();
    } else if (xhr.status === 400) {
        alertify.alert()
            .setting({
                title: 'Error!',
                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + xhr.responseJSON.message
            }).show();
    } else {
        alertify.alert()
            .setting({
                title: 'Server Error',
                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
            }).show();
    }
}