<!-- Add Period Modal -->
<div class="modal fade" id="addPeriodModal" tabindex="-1" aria-labelledby="addPeriodModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addPeriodModalLabel">Add New Period</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="period_name" name="name">
                </div>
                <div class="mb-3">
                    <label for="start_time" class="form-label">Start Time <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" id="period_start_time" name="start_time">
                </div>
                <div class="mb-3">
                    <label for="end_time" class="form-label">End Time <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" id="period_end_time" name="end_time">
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="period_status" class="form-select">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addPeriod()">Save Period</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addPeriod(){
        const name = $('#period_name').val();
        const start_time = $('#period_start_time').val();
        const end_time = $('#period_end_time').val();
        const status = $('#period_status').val();

        if(name == '' || start_time == '' || end_time == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('periods.store') }}",
            data: {
                'name': name,
                'start_time': start_time,
                'end_time': end_time,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addPeriodModal').modal('hide');
                    $('#period_name').val('');
                    $('#period_start_time').val('');
                    $('#period_end_time').val('');
                    $('#period_status').val('active');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>