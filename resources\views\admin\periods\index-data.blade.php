@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->name ?? '' }}</td>
        <td>{{ $row->start_time_format ?? '' }}</td>
        <td>{{ $row->end_time_format ?? '' }}</td>
        <td class="text-center">
            @include('admin.includes.status')
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Period" onclick="editPeriod({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_period_name_data{{ $row->id }}" value="{{ $row->name ?? '' }}">
                <input type="hidden" id="edit_period_start_time_data{{ $row->id }}" value="{{ date('H:i', strtotime($row->start_time)) }}">
                <input type="hidden" id="edit_period_end_time_data{{ $row->id }}" value="{{ date('H:i', strtotime($row->end_time)) }}">
                <input type="hidden" id="edit_period_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Section" onclick="deletePeriod({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="6" class="text-center">No Periods found.</td>
    </tr>
@endforelse