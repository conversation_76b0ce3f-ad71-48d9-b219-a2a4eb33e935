@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Periods')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Periods Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPeriodModal">
                <i class="fas fa-plus"></i> Add Period
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="period_name_search" placeholder="Search by Name...">
            </div>
            <div class="col-md-3">
                <select name="status" id="period_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>

        <!-- Periods Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.periods.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Edit Period Modal -->
    <div class="modal fade" id="editPeriodModal" tabindex="-1" aria-labelledby="editPeriodModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editPeriodModalLabel">Update Period</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_period_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Period Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_period_name" name="name">
                    </div>
                    <div class="mb-3">
                        <label for="edit_start_time" class="form-label">Start Time <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="edit_period_start_time" name="start_time">
                    </div>
                    <div class="mb-3">
                        <label for="edit_end_time" class="form-label">End Time <span class="text-danger">*</span></label>
                        <input type="time" class="form-control" id="edit_period_end_time" name="end_time">
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_period_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updatePeriod()">Update Period</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Search Filter
        function search(){
            const name = $('#period_name_search').val();
            const status = $('#period_status_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('periods.index') }}",
                data: {
                    'name': name,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#period_name_search').val('');
            $('#period_status_search').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            const url = $(this).attr('href');
            const name = $('#period_name_search').val();
            const status = $('#period_status_search').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'name': name,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        // Edit 
        function editPeriod(id){
            $('#edit_period_id').val(id);
            $('#edit_period_name').val($('#edit_period_name_data'+id).val());
            $('#edit_period_start_time').val($('#edit_period_start_time_data'+id).val());
            $('#edit_period_end_time').val($('#edit_period_end_time_data'+id).val());
            $('#edit_period_status').val($('#edit_period_status_data'+id).val());
            $('#editPeriodModal').modal('show')
        }

        // Update
        function updatePeriod(){
            const id = $('#edit_period_id').val();
            const name = $('#edit_period_name').val();
            const start_time = $('#edit_period_start_time').val();
            const end_time = $('#edit_period_end_time').val();
            const status = $('#edit_period_status').val();

            if(name == '' || start_time == '' || end_time == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('periods.update', ':id') }}".replace(':id', id),
                data: {
                    'name': name,
                    'start_time': start_time,
                    'end_time': end_time,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editPeriodModal').modal('hide');
                        $('#edit_period_name').val('');
                        $('#edit_period_start_time').val('');
                        $('#edit_period_end_time').val('');
                        $('#edit_period_status').val('active');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        let firstError = Object.values(errors)[0][0]; // first error message

                        alertify.alert()
                            .setting({
                                title: 'Validation Error',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                            }).show();
                    } else {
                        alertify.alert()
                            .setting({
                                title: 'Server Error',
                                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                            }).show();
                    }
                }
            });
        }
    </script>

    @include('admin.periods.add-modal')
    <x-delete-confirm functionName="deletePeriod" deleteUrl="periods.destroy" />
@endpush