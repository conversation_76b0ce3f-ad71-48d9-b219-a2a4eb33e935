<!-- Add Section Modal -->
<div class="modal fade" id="addSectionModal" tabindex="-1" aria-labelledby="addSectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addSectionModalLabel">Add New Section</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                    <select name="class_id" id="section_class_id" class="form-select" required>
                        <option value="">Select here ..</option>
                        @foreach ($classes as $id => $name)
                            <option value="{{ $id }}" @selected(old('class_id') == $id)>{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="section_name" name="name" value="{{ old('name') }}" maxlength="50" required>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="section_status" class="form-select" required>
                        <option value="active" @selected(old('status') == "active")>Active</option>
                        <option value="inactive" @selected(old('status') == "inactive")>Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addSection()">Save Section</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addSection(){
        const class_id = $('#section_class_id').val();
        const name = $('#section_name').val();
        const status = $('#section_status').val();

        if(class_id == '' || name == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('sections.store') }}",
            data: {
                'class_id': class_id,
                'name': name,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addSectionModal').modal('hide');
                    $('#section_class_id').val('');
                    $('#section_name').val('');
                    alertify.success(response.message);

                    // If teacher subject is true then open teacher subject modal
                    window.currentRouteName = "{{ Route::currentRouteName() }}";
                    if (window.currentRouteName && window.currentRouteName.startsWith('teacher-subjects')) {
                        selectClass();
                        selectClassForEdit();
                        $('#addTeacherSubjectModal').modal('show');
                    }

                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>