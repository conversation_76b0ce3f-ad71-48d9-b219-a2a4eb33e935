<!-- Add Session Master Modal -->
<div class="modal fade" id="addSessionMasterModal" tabindex="-1" aria-labelledby="addSessionMasterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addSessionMasterModalLabel">Add New Session Master</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Session Master Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="session_master_name" name="name">
                </div>
                <div class="mb-3">
                    <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="start_date" name="start_date">
                </div>
                <div class="mb-3">
                    <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="end_date" name="end_date">
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="session_master_status" class="form-select">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addSessionMaster()">Save Session Master</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addSessionMaster(){
        const name = $('#session_master_name').val();
        const start_date = $('#start_date').val();
        const end_date = $('#end_date').val();
        const status = $('#session_master_status').val();

        if(name == '' || start_date == '' || end_date == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('session-masters.store') }}",
            data: {
                'name': name,
                'start_date': start_date,
                'end_date': end_date,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addSessionMasterModal').modal('hide');
                    $('#session_master_name').val('');
                    $('#start_date').val('');
                    $('#end_date').val('');
                    $('#session_master_status').val('active');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            @include('admin.includes.ajax-error')
        });

    }
</script>