@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->name ?? '' }}</td>
        <td>{{ $row->start_date_format ?? '' }}</td>
        <td>{{ $row->end_date_format ?? '' }}</td>
        <td class="text-center">
            @include('admin.includes.status')
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Session Master" onclick="editSessionMaster({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_session_master_name_data{{ $row->id }}" value="{{ $row->name ?? '' }}">
                <input type="hidden" id="edit_session_master_start_date_data{{ $row->id }}" value="{{ $row->start_date }}">
                <input type="hidden" id="edit_session_master_end_date_data{{ $row->id }}" value="{{ $row->end_date }}">
                <input type="hidden" id="edit_session_master_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="6" class="text-center">No Session Masters found.</td>
    </tr>
@endforelse