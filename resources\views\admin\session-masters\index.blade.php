@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Session Masters')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Session Masters Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSessionMasterModal">
                <i class="fas fa-plus"></i> Add Session Master
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="session_master_name_search" placeholder="Search by Name...">
            </div>
            <div class="col-md-3">
                <select name="status" id="session_master_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>

        <!-- Session Masters Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="SessionMastersTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.session-masters.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Edit Session Master Modal -->
    <div class="modal fade" id="editSessionMasterModal" tabindex="-1" aria-labelledby="editSessionMasterModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editSessionMasterModalLabel">Update Session Master</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_session_master_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Session Master Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_session_master_name" name="name">
                    </div>
                    <div class="mb-3">
                        <label for="edit_start_time" class="form-label">Start Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="edit_session_master_start_date" name="start_date">
                    </div>
                    <div class="mb-3">
                        <label for="edit_end_time" class="form-label">End Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="edit_session_master_end_date" name="end_date">
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_session_master_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateSessionMaster()">Update Session Master</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Search Filter
        function search(){
            const name = $('#session_master_name_search').val();
            const status = $('#session_master_status_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('session-masters.index') }}",
                data: {
                    'name': name,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#session_master_name_search').val('');
            $('#session_master_status_search').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            const url = $(this).attr('href');
            const name = $('#session_master_name_search').val();
            const status = $('#session_master_status_search').val();

            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'name': name,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        // Edit 
        function editSessionMaster(id){
            $('#edit_session_master_id').val(id);
            $('#edit_session_master_name').val($('#edit_session_master_name_data'+id).val());
            $('#edit_session_master_start_date').val($('#edit_session_master_start_date_data'+id).val());
            $('#edit_session_master_end_date').val($('#edit_session_master_end_date_data'+id).val());
            $('#edit_session_master_status').val($('#edit_session_master_status_data'+id).val());
            $('#editSessionMasterModal').modal('show');
        }

        // Update
        function updateSessionMaster(){
            const id = $('#edit_session_master_id').val();
            const name = $('#edit_session_master_name').val();
            const start_date = $('#edit_session_master_start_date').val();
            const end_date = $('#edit_session_master_end_date').val();
            const status = $('#edit_session_master_status').val();

            if(name == '' || start_date == '' || end_date == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('session-masters.update', ':id') }}".replace(':id', id),
                data: {
                    'name': name,
                    'start_date': start_date,
                    'end_date': end_date,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editSessionMasterModal').modal('hide');
                        $('#edit_session_master_name').val('');
                        $('#edit_session_master_start_date').val('');
                        $('#edit_session_master_end_date').val('');
                        $('#edit_session_master_status').val('active');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                @include('admin.includes.ajax-error')
            });
        }
    </script>

    @include('admin.session-masters.add-modal')
@endpush