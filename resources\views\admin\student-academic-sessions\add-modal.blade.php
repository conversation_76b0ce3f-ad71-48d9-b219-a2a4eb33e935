<!-- Add Student Academic Session Modal -->
<div class="modal fade" id="addStudentAcademicSessionModal" tabindex="-1" aria-labelledby="addStudentAcademicSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addStudentAcademicSessionModalLabel">Add New Student Academic Session</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="student_id" class="form-label">Student <span class="text-danger">*</span></label>
                    <select name="student_id" id="student_academic_session_student_id" class="form-select" required>
                        <option value="">Select here ..</option>
                        
                    </select>
                </div>
                <div class="mb-3">
                    <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                    <select name="class_id" id="student_academic_session_class_id" class="form-select" onchange="selectSections()" required>
                        <option value="">Select here ..</option>
                        
                    </select>
                </div>
                <div class="mb-3">
                    <label for="section_id" class="form-label">Section </label>
                    <select name="section_id" id="student_academic_session_section_id" class="form-select" required>
                        <option value="">Select here ..</option>
                        
                    </select>
                </div>
                <div class="mb-3">
                    <label for="roll_no" class="form-label">Roll No <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="student_academic_session_roll_no" name="roll_no">  
                </div>
                <div class="mb-3">
                    <label for="admission_date" class="form-label">Admission Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control" id="student_academic_session_admission_date" name="admission_date">  
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="student_academic_session_status" class="form-select" required>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addStudentAcademicSession()">Save Student Academic Session</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addStudentAcademicSession(){
        const student_id = $('#student_academic_session_student_id').val();
        const class_id = $('#student_academic_session_class_id').val();    
        const section_id = $('#student_academic_session_section_id').val();    
        const roll_no = $('#student_academic_session_roll_no').val();    
        const admission_date = $('#student_academic_session_admission_date').val();    
        const status = $('#student_academic_session_status').val();

        if(student_id == '' || class_id == '' || roll_no == '' || admission_date == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All Red marked fields is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('student-academic-sessions.store') }}",
            data: {
                'student_id': student_id,   
                'class_id': class_id,   
                'section_id': section_id,    
                'roll_no': roll_no,    
                'admission_date': admission_date,    
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addStudentAcademicSessionModal').modal('hide');
                    $('#student_academic_session_student_id').val('');
                    $('#student_academic_session_session_master_id').val('');
                    $('#student_academic_session_class_id').val('');
                    $('#student_academic_session_section_id').val('');
                    $('#student_academic_session_roll_no').val('');
                    $('#student_academic_session_admission_date').val('');
                    $('#student_academic_session_status').val('active');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            @include('admin.includes.ajax-error')
        });

    }
</script>