@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->student->user->name ?? '' }}</td>
        <td>{{ $row->class->name ?? '' }}</td>
        <td>{{ $row->section->name ?? '' }}</td>
        <td>{{ $row->roll_no ?? '' }}</td>
        <td>{{ $row->admission_date ?? '' }}</td>
        <td class="text-center">
            @include('admin.includes.status')
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Student Academic Session" onclick="editStudentAcademicSession({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_student_academic_session_student_id_data{{ $row->id }}" value="{{ $row->student->id ?? '' }}">
                <input type="hidden" id="edit_student_academic_session_class_id_data{{ $row->id }}" value="{{ $row->class->id ?? '' }}">
                <input type="hidden" id="edit_student_academic_session_section_id_data{{ $row->id }}" value="{{ $row->section->id ?? '' }}">
                <input type="hidden" id="edit_student_academic_session_roll_no_data{{ $row->id }}" value="{{ $row->roll_no ?? '' }}">
                <input type="hidden" id="edit_student_academic_session_admission_date_data{{ $row->id }}" value="{{ date('Y-m-d', strtotime($row->admission_date ?? '')) }}">
                <input type="hidden" id="edit_student_academic_session_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Student Academic Session" onclick="deleteStudentAcademicSession({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="8" class="text-center">No Student Academic Sessions found.</td>
    </tr>
@endforelse