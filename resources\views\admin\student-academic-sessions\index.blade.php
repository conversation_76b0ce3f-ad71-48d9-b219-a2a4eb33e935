@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Student Academic Sessions')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Student Academic Sessions Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentAcademicSessionModal">
                <i class="fas fa-plus"></i> Add Student Academic Session
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="st_ac_se_name_search" placeholder="Student...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="st_ac_se_class_search" placeholder="Class...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="st_ac_se_section_search" placeholder="Section...">
            </div>
            <div class="col-md-3">
                <select name="status" id="student_status" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>


        <!-- Student Academic Sessions Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Student</th>
                        <th class="text-center">Class</th>
                        <th class="text-center">Section</th>
                        <th class="text-center">Roll No</th>
                        <th class="text-center">Admission Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.student-academic-sessions.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Add Student Academic Session Modal -->
    @include('admin.student-academic-sessions.add-modal')

    <!-- Edit Student Academic Session Modal -->
    <div class="modal fade" id="editStudentAcademicSessionModal" tabindex="-1" aria-labelledby="editStudentAcademicSessionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editStudentAcademicSessionModalLabel">Update Student Academic Session</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_student_academic_session_id">
                    <div class="mb-3">
                        <label for="edit_student_academic_session_student_id" class="form-label">Student <span class="text-danger">*</span></label>
                        <select name="student_id" id="edit_student_academic_session_student_id" class="form-select" required>
                            <option value="">Select here ..</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_student_academic_session_class_id" class="form-label">Class <span class="text-danger">*</span></label>
                        <select name="class_id" id="edit_student_academic_session_class_id" class="form-select" onchange="selectEditSections()" required>
                            <option value="">Select here ..</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_student_academic_session_section_id" class="form-label">Section </label>
                        <select name="section_id" id="edit_student_academic_session_section_id" class="form-select" required>
                            <option value="">Select here ..</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_student_academic_session_roll_no" class="form-label">Roll No <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_student_academic_session_roll_no" name="roll_no">  
                    </div>
                    <div class="mb-3">
                        <label for="edit_student_academic_session_admission_date" class="form-label">Admission Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="edit_student_academic_session_admission_date" name="admission_date">  
                    </div>
                    <div class="mb-3">
                        <label for="edit_student_academic_session_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_student_academic_session_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateStudentAcademicSession()">Update Student Academic Session</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function search(){
            const name = $('#st_ac_se_name_search').val();
            const email = $('#st_ac_se_class_search').val();
            const phone = $('#st_ac_se_section_search').val();
            const status = $('#student_status').val();

            $.ajax({
                type: "GET",
                url: "{{ route('student-academic-sessions.index') }}",
                data: {
                    'student_name': name,
                    'class_name': email,
                    'section_name': phone,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#st_ac_se_name_search').val('');
            $('#st_ac_se_class_search').val('');
            $('#st_ac_se_section_search').val('');
            $('#student_status').val('active');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const name = $('#st_ac_se_name_search').val();
            const email = $('#st_ac_se_class_search').val();
            const phone = $('#st_ac_se_section_search').val();
            const status = $('#student_status').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'student_name': name,
                    'class_name': email,
                    'section_name': phone,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });

        // Get students
        function getStudents(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.students') }}",
                success: function (response) {
                    $('#student_academic_session_student_id').html(response.html);
                    $('#edit_student_academic_session_student_id').html(response.html);
                    $('#st_ac_se_name_search').html(response.html);
                }
            });
        }

        // Get session masters
        function getSessionMasters(){
            $.ajax({
                type: "GET",
                url: "{{ route('dropdown.session-masters') }}",
                success: function (response) {
                    $('#student_academic_session_session_master_id').html(response.html);
                }
            });
        }

        // Get classes
        function getClasses(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.classes') }}",
                success: function (response) {
                    $('#student_academic_session_class_id').html(response.html);
                    $('#edit_student_academic_session_class_id').html(response.html);
                }
            });
        }

        function selectSections(){
            const class_id = $('#student_academic_session_class_id').val();
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#student_academic_session_section_id').html(response.html);
                }
            });
        }

        function selectEditSections(sectionId = null){
            const class_id = $('#edit_student_academic_session_class_id').val();
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#edit_student_academic_session_section_id').html(response.html);
                    if(sectionId != null){
                        $('#edit_student_academic_session_section_id').val(sectionId);
                    }
                }
            });
        }

        getStudents();
        getSessionMasters();
        getClasses();

        // Edit Student Academic Session
        function editStudentAcademicSession(id){
            $('#edit_student_academic_session_id').val(id);
            $('#edit_student_academic_session_student_id').val($('#edit_student_academic_session_student_id_data'+id).val());
            $('#edit_student_academic_session_class_id').val($('#edit_student_academic_session_class_id_data'+id).val());
            //$('#edit_student_academic_session_section_id').val($('#edit_student_academic_session_section_id_data'+id).val());
            $('#edit_student_academic_session_roll_no').val($('#edit_student_academic_session_roll_no_data'+id).val());
            $('#edit_student_academic_session_admission_date').val($('#edit_student_academic_session_admission_date_data'+id).val());
            $('#edit_student_academic_session_status').val($('#edit_student_academic_session_status_data'+id).val());
            const sectionId = $('#edit_student_academic_session_section_id_data'+id).val();
            selectEditSections(sectionId);
            $('#editStudentAcademicSessionModal').modal('show');
        }

        // Update Student Academic Session
        function updateStudentAcademicSession(){
            const id = $('#edit_student_academic_session_id').val();
            const student_id = $('#edit_student_academic_session_student_id').val();
            const class_id = $('#edit_student_academic_session_class_id').val();    
            const section_id = $('#edit_student_academic_session_section_id').val();    
            const roll_no = $('#edit_student_academic_session_roll_no').val();    
            const admission_date = $('#edit_student_academic_session_admission_date').val();    
            const status = $('#edit_student_academic_session_status').val();

            if(student_id == '' || class_id == '' || roll_no == '' || admission_date == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All Red marked fields is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('student-academic-sessions.update', ':id') }}".replace(':id', id),
                data: {
                    'student_id': student_id,
                    'class_id': class_id,
                    'section_id': section_id,
                    'roll_no': roll_no,
                    'admission_date': admission_date,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editStudentAcademicSessionModal').modal('hide');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                @include('admin.includes.ajax-error')
            });
        }
    </script>
    <x-delete-confirm functionName="deleteStudentAcademicSession" deleteUrl="student-academic-sessions.destroy" />
@endpush