@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>
            <div class="member-info">
                <div class="member-avatar">
                    {{ strtoupper(substr($row->student->user->name ?? 'S', 0, 1)) }}
                </div>
                <div class="member-name">{{ $row->student->user->name ?? '' }}</div>
            </div>
        </td>
        <td class="member-phone">{{ $row->parent_contact_no ?? 'N/A' }}</td>
        <td>{{ date('d-m-Y') }}</td>
        <td id="check_in_{{ $row->student->id }}">
            @if(isset($row->student->studentAttendance->status))
                @if($row->student->studentAttendance->check_in_time)
                    {{ $row->student->studentAttendance->check_in_time }}
                @else
                    -
                @endif
            @else
                <button class="check-in-btn"
                        onclick="presentAttendance({{ $row->student->id }})"
                        id="present_btn_{{ $row->student->id }}">
                    Check-In
                </button>
            @endif
        </td>
        <td id="check_out_{{ $row->student->id }}">
            @if(isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'present')
                Not checked out yet!
            @else
                -
            @endif
        </td>
        <td id="status_{{ $row->student->id }}">
            @if(isset($row->student->studentAttendance->status))
                @if($row->student->studentAttendance->status == 'present')
                    <span class="status-present">Present</span>
                @else
                    <span class="status-absent">Absent</span>
                @endif
            @else
                <div class="attendance-actions">
                    <button class="btn-present"
                            onclick="presentAttendance({{ $row->student->id }})"
                            id="present_btn_alt_{{ $row->student->id }}">
                        Present
                    </button>
                    <button class="btn-absent"
                            onclick="absentAttendance({{ $row->student->id }})"
                            id="absent_btn_{{ $row->student->id }}">
                        Absent
                    </button>
                </div>
            @endif
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7">
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h5>No Student Attendances Found</h5>
                <p>Please select a class and section to view attendance records.</p>
            </div>
        </td>
    </tr>
@endforelse