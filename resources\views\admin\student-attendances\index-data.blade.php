@forelse($records as $row)
    <tr>
        <td>
            <div class="fw-bold text-primary">{{ $loop->iteration }}</div>
        </td>
        <td>
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <img src="{{ $row->student->user->photo ? asset('storage/'.$row->student->user->photo) : asset('assets/images/default-avatar.png') }}"
                         alt="Student"
                         class="rounded-circle"
                         width="40"
                         height="40"
                         style="object-fit: cover;">
                </div>
                <div>
                    <div class="fw-semibold text-dark">{{ $row->student->user->name ?? '' }}</div>
                    <small class="text-muted">{{ $row->student->user->email ?? '' }}</small>
                </div>
            </div>
        </td>
        <td>
            <span class="badge bg-info">{{ $row->roll_no ?? 'N/A' }}</span>
        </td>
        <td>
            <div class="fw-semibold">{{ date('d-m-Y') }}</div>
            <small class="text-muted">{{ date('l') }}</small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-attendance btn-present"
                        title="Mark Present"
                        onclick="presentAttendance({{ $row->student->id }})"
                        {{ isset($row->student->studentAttendance->status) ? 'disabled' : '' }}
                        id="present_btn_{{ $row->student->id }}">
                    <i class="fas fa-check me-1"></i>Present
                </button>
                <button class="btn btn-attendance btn-absent"
                        title="Mark Absent"
                        onclick="absentAttendance({{ $row->student->id }})"
                        {{ isset($row->student->studentAttendance->status) ? 'disabled' : '' }}
                        id="absent_btn_{{ $row->student->id }}">
                    <i class="fas fa-times me-1"></i>Absent
                </button>
            </div>
        </td>
        <td id="marked_by{{ $row->student->id }}">
            @if($row->student->studentAttendance->markedBy ?? null)
                <div class="fw-semibold">{{ $row->student->studentAttendance->markedBy->name }}</div>
                <small class="text-muted">{{ $row->student->studentAttendance->created_at->format('H:i A') }}</small>
            @else
                <span class="text-muted">-</span>
            @endif
        </td>
        <td id="check_in_time{{ $row->student->id }}">
            @if($row->student->studentAttendance->check_in_time ?? null)
                <div class="fw-semibold text-success">
                    <i class="fas fa-clock me-1"></i>{{ $row->student->studentAttendance->check_in_time }}
                </div>
            @else
                <span class="text-muted">-</span>
            @endif
        </td>
        <td class="text-center" id="status{{ $row->student->id }}">
            <span class="status-badge status-present"
                  id="present_status{{ $row->student->id }}"
                  style="{{ isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'present' ? '' : 'display:none' }}">
                <i class="fas fa-check me-1"></i>Present
            </span>

            <span class="status-badge status-absent"
                  id="absent_status{{ $row->student->id }}"
                  style="{{ isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'absent' ? '' : 'display:none' }}">
                <i class="fas fa-times me-1"></i>Absent
            </span>

            <span class="status-badge status-not-marked"
                  id="not_marked_status{{ $row->student->id }}"
                  style="{{ isset($row->student->studentAttendance->status) ? 'display:none' : '' }}">
                <i class="fas fa-question me-1"></i>Not Marked
            </span>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="8" class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                <h5>No Student Attendances Found</h5>
                <p>Please select a class and section to view attendance records.</p>
            </div>
        </td>
    </tr>
@endforelse