@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->student->user->name ?? '' }}</td>
        <td>{{ $row->roll_no ?? '' }}</td>
        <td>{{ date('d-m-Y') }}</td>
        <td>
            <button class="btn btn-sm btn-primary" title="Present Attendance" onclick="presentAttendance({{ $row->student->id }})"
                {{ isset($row->student->studentAttendance->status) ? 'disabled' : '' }}
                id="present_btn_{{ $row->student->id }}"
            >
                Present
            </button>
            <button class="btn btn-sm btn-danger" title="Absent Attendance" onclick="absentAttendance({{ $row->student->id }})"
                {{ isset($row->student->studentAttendance->status) ? 'disabled' : '' }}
                id="absent_btn_{{ $row->student->id }}"
            >
                Absent
            </button>
        </td>
        <td id="marked_by{{ $row->student->id }}">{{ $row->student->studentAttendance->markedBy->name ?? '' }}</td>
        <td id="check_in_time{{ $row->student->id }}">{{ $row->student->studentAttendance->check_in_time ?? '' }}</td>
        <td class="text-center" id="status{{ $row->student->id }}">
            
                <span class="badge bg-success" id="present_status{{ $row->student->id }}" style="{{ isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'present' ? '' : 'display:none' }}">{{ ucfirst($row->student->studentAttendance->status ?? '') }}</span>
            
                <span class="badge bg-danger" id="absent_status{{ $row->student->id }}" style="{{ isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'absent' ? '' : 'display:none' }}">{{ ucfirst($row->student->studentAttendance->status ?? '') }}</span>
            
                <span class="badge bg-warning" id="not_marked_status{{ $row->student->id }}" style="{{ isset($row->student->studentAttendance->status) ? 'display:none' : '' }}">Not Marked</span>
            
        </td>
    </tr>
@empty
    <tr>
        <td colspan="8" class="text-center">No Student Attendances found.</td>
    </tr>
@endforelse