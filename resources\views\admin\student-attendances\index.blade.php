@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Student Attendances')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Student Attendances</h4>
        </div>
        <div class="row align-items-center g-2 mb-4">
                <div class="col-md-3">
                    <select name="class_id" id="class_id" class="form-select" onchange="sections()">
                        <option value="">Select Class</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="section_id" id="section_id" class="form-select">
                        <option value="">Select Section</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-success px-4" onclick="search()">
                        <i class="fa fa-search me-1"></i> Search
                    </button>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-secondary px-4" onclick="resetSearchForm()">
                        <i class="fa fa-undo me-1"></i> Reset
                    </button>
                </div>
        </div>
        <div class="row">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="ClassesTable">
                    <thead class="table-dark">
                        <tr>
                            <th>#</th>
                            <th>Student</th>
                            <th>Roll No</th>
                            <th>Attendance Date</th>
                            <th>Mark Attendance</th>
                            <th>Marked By</th>
                            <th>Check In Time</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="list_data">
                        @include('admin.student-attendances.index-data')
                    </tbody>
                </table>
                <div class="mt-2" id="pagination_links">
                    {{ $records->links('pagination::bootstrap-5') }}
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function search(){
            const class_id = $('#class_id').val();
            const section_id = $('#section_id').val();

            $.ajax({
                type: "GET",
                url: "{{ route('student-attendances.index') }}",
                data: {
                    'class_id': class_id,
                    'section_id': section_id
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#class_id').val('');
            $('#section_id').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const class_id = $('#class_id').val();
            const section_id = $('#section_id').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'class_id': class_id,
                    'section_id': section_id
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        function classes(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.classes') }}",
                success: function (response) {
                    $('#class_id').html(response.html);
                }
            });
        }

        function sections(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': $('#class_id').val(),
                },
                success: function (response) {
                    $('#section_id').html(response.html);
                }
            });
        }

        classes();
        sections();
    </script>   
    <script>
        function presentAttendance(id){
            $.ajax({
                type: "GET",
                url: "{{ route('student-attendances.present') }}",
                data: {
                    'id': id,
                },
                success: function (response) {
                    if(response.status == true){
                        $('#present_btn_' + id).attr('disabled', true);
                        $('#absent_btn_' + id).attr('disabled', true);
                        $('#check_in_time' + id).html(response.check_in_time);
                        $('#present_status' + id).html('Present');
                        $('#present_status' + id).show();
                        $('#absent_status' + id).hide();
                        $('#not_marked_status' + id).hide();
                        $('#marked_by' + id).html(response.marked_by);
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }
        function absentAttendance(id){
            $.ajax({
                type: "GET",
                url: "{{ route('student-attendances.absent') }}",
                data: {
                    'id': id,
                },
                success: function (response) {
                    if(response.status == true){
                        $('#present_btn_' + id).attr('disabled', true);
                        $('#absent_btn_' + id).attr('disabled', true);
                        $('#check_in_time' + id).html(response.check_in_time);
                        $('#present_status' + id).hide();
                        $('#absent_status' + id).html('Absent');
                        $('#absent_status' + id).show();
                        $('#not_marked_status' + id).hide();
                        $('#marked_by' + id).html(response.marked_by);
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }
    </script>
@endpush