@extends('layouts.admin')
@section('title', 'Student Attendances')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.1);
        z-index: 1;
    }

    .page-header-content {
        position: relative;
        z-index: 2;
        padding: 2rem;
    }

    .page-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        opacity: 0.9;
        font-size: 1rem;
        margin-bottom: 0;
    }

    .filter-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .filter-header {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .filter-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }

    .filter-body {
        padding: 1.5rem;
    }

    .form-select, .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.8);
    }

    .form-select:focus, .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
    }

    .btn-modern {
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-search {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .btn-search:hover {
        background: linear-gradient(45deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }

    .btn-reset {
        background: linear-gradient(45deg, #6c757d, #495057);
        color: white;
    }

    .btn-reset:hover {
        background: linear-gradient(45deg, #495057, #343a40);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }

    .data-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        overflow: hidden;
    }

    .data-card-header {
        background: linear-gradient(45deg, #343a40, #495057);
        color: white;
        padding: 1rem 1.5rem;
        font-weight: 600;
    }

    .table-modern {
        margin-bottom: 0;
    }

    .table-modern thead th {
        background: linear-gradient(45deg, #343a40, #495057);
        color: white;
        border: none;
        padding: 1rem 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .table-modern tbody td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f8f9fa;
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-present {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .status-absent {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .status-not-marked {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }

    .btn-attendance {
        padding: 0.4rem 0.8rem;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        border: none;
        margin: 0 0.2rem;
        transition: all 0.3s ease;
    }

    .btn-present {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .btn-present:hover {
        background: linear-gradient(45deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-1px);
    }

    .btn-absent {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .btn-absent:hover {
        background: linear-gradient(45deg, #fd7e14, #ffc107);
        color: white;
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .page-header-content {
            padding: 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .filter-body {
            padding: 1rem;
        }

        .btn-modern {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <x-alert />

    <!-- Page Header -->
    <div class="page-header">
        <div class="page-header-content">
            <h1 class="page-title">
                <i class="fas fa-calendar-check me-3"></i>Student Attendances
            </h1>
            <p class="page-subtitle">Manage and track student attendance records</p>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="card filter-card">
        <div class="filter-header">
            <h5 class="filter-title">
                <i class="fas fa-filter me-2"></i>Filter Options
            </h5>
        </div>
        <div class="filter-body">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-school me-1"></i>Class
                    </label>
                    <select name="class_id" id="class_id" class="form-select" onchange="sections()">
                        <option value="">Select Class</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-users me-1"></i>Section
                    </label>
                    <select name="section_id" id="section_id" class="form-select">
                        <option value="">Select Section</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-calendar me-1"></i>Date
                    </label>
                    <input type="date" id="attendance_date" class="form-control" value="{{ date('Y-m-d') }}">
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-modern btn-search" onclick="search()">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-modern btn-reset" onclick="resetSearchForm()">
                        <i class="fas fa-undo me-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Card -->
    <div class="card data-card">
        <div class="table-responsive">
            <table class="table table-modern" id="AttendanceTable">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="20%">Student</th>
                        <th width="10%">Roll No</th>
                        <th width="12%">Date</th>
                        <th width="18%">Mark Attendance</th>
                        <th width="15%">Marked By</th>
                        <th width="10%">Check In</th>
                        <th width="10%">Status</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.student-attendances.index-data')
                </tbody>
            </table>
        </div>
        <div class="p-3" id="pagination_links">
            {{ $records->links('pagination::bootstrap-5') }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <script>
        function search(){
            const class_id = $('#class_id').val();
            const section_id = $('#section_id').val();

            $.ajax({
                type: "GET",
                url: "{{ route('student-attendances.index') }}",
                data: {
                    'class_id': class_id,
                    'section_id': section_id
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#class_id').val('');
            $('#section_id').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const class_id = $('#class_id').val();
            const section_id = $('#section_id').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'class_id': class_id,
                    'section_id': section_id
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        function classes(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.classes') }}",
                success: function (response) {
                    $('#class_id').html(response.html);
                }
            });
        }

        function sections(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': $('#class_id').val(),
                },
                success: function (response) {
                    $('#section_id').html(response.html);
                }
            });
        }

        classes();
        sections();
    </script>   
    <script>
        function presentAttendance(id){
            $.ajax({
                type: "GET",
                url: "{{ route('student-attendances.present') }}",
                data: {
                    'id': id,
                },
                success: function (response) {
                    if(response.status == true){
                        $('#present_btn_' + id).attr('disabled', true);
                        $('#absent_btn_' + id).attr('disabled', true);
                        $('#check_in_time' + id).html(response.check_in_time);
                        $('#present_status' + id).html('Present');
                        $('#present_status' + id).show();
                        $('#absent_status' + id).hide();
                        $('#not_marked_status' + id).hide();
                        $('#marked_by' + id).html(response.marked_by);
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }
        function absentAttendance(id){
            $.ajax({
                type: "GET",
                url: "{{ route('student-attendances.absent') }}",
                data: {
                    'id': id,
                },
                success: function (response) {
                    if(response.status == true){
                        $('#present_btn_' + id).attr('disabled', true);
                        $('#absent_btn_' + id).attr('disabled', true);
                        $('#check_in_time' + id).html(response.check_in_time);
                        $('#present_status' + id).hide();
                        $('#absent_status' + id).html('Absent');
                        $('#absent_status' + id).show();
                        $('#not_marked_status' + id).hide();
                        $('#marked_by' + id).html(response.marked_by);
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }
    </script>
@endpush