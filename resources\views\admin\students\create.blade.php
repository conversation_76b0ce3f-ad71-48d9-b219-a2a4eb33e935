@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Add Student')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>
                @isset($student)
                    Update Student
                @else
                    Add Student
                @endisset
            </h4>
            <a class="btn btn-primary" href="{{ route('students.index') }}">
                <i class="fas fa-list"></i> Students List
            </a>
        </div>

        <!-- Modern Filter UI --> 
        <div class="card shadow">
            <div class="card-body" style="background-color:rgb(191, 194, 194)">
                @if(isset($student))
                    <form action="{{ route('students.update',$student->id) }}" method="POST">
                    @csrf
                    @method('PUT')
                @else
                <form action="{{ route('students.store') }}" method="POST">
                    @csrf
                @endif
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ old('name',$student->user->name ?? '') }}" maxlength="70" required>  
                        </div>
                        <div class="col-md-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" name="email" value="{{ old('email',$student->user->email ?? '') }}" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="password" class="form-label">Password 
                                @empty($student->user->password)
                                    <span class="text-danger">*</span>
                                @endempty
                            </label>
                            <input type="password" name="password" class="form-control" value="{{ old('password') }}" id="password" maxlength="20"
                                @empty($student->user->password) required @endempty >  
                        </div>
                        <div class="col-md-3">
                            <label for="father_name" class="form-label">Father Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="father_name" value="{{ old('father_name',$student->father_name ?? '') }}" maxlength="70" required>
                        </div>
                        <div class="col-md-3">
                            <label for="mother_name" class="form-label">Mother Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="mother_name" value="{{ old('mother_name',$student->mother_name ?? '') }}" maxlength="70" required>
                        </div>
                        <div class="col-md-3">
                            <label for="parent_contact_no" class="form-label">Parent Contact No. <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" value="{{ old('parent_contact_no',$student->parent_contact_no ?? '') }}" name="parent_contact_no" required>
                        </div>
                        <div class="col-md-3">
                            <label for="father_occupation" class="form-label">Father Occupation</label>
                            <input type="text" class="form-control" value="{{ old('father_occupation',$student->father_occupation ?? '') }}" name="father_occupation" maxlength="70">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="dob" class="form-label">DOB <span class="text-danger">*</span></label>
                            <input type="text" class="form-control datepicker" value="{{ old('dob',$student->dob ?? '') }}" id="dob" name="dob" autocomplete="OFF" required>  
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="gender" class="form-label">Gender <span class="text-danger"></span></label>
                            <select name="gender" id="gender" class="form-select" required>
                                <option value="">Select Gender</option>
                                <option value="Male" {{ old('gender',$student->gender ?? '') == 'Male' ? 'selected' : '' }}>Male</option>
                                <option value="Female" {{ old('gender',$student->gender ?? '') == 'Female' ? 'selected' : '' }}>Female</option>
                                <option value="Transgender" {{ old('gender',$student->gender ?? '') == 'Transgender' ? 'selected' : '' }}>Transgender</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="blood_group" class="form-label">Blood Group</label>
                            <select name="blood_group" id="blood_group" class="form-select">
                                <option value="">Select Blood Group</option>
                                @foreach ($bloodGroups as $key => $value)
                                    <option value="{{ $key }}" {{ old('blood_group',$student->blood_group ?? '') == $key ? 'selected' : '' }}>{{ $value }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="caste" class="form-label"> Caste <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="caste" value="{{ old('caste',$student->caste ?? '') }}" maxlength="20" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-select" required>
                                <option value="active" {{ old('status',$student->user->status ?? '') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status',$student->user->status ?? '') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="address" class="form-label"> Address <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="address" value="{{ old('address',$student->user->address ?? '') }}" maxlength="255" required>
                        </div>


                        {{-- <div class="col-md-3 mb-3">
                            <label for="admission_date" class="form-label">Admission Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" name="admission_date" maxlength="70" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                            <select name="class_id" id="student_class_id" class="form-select" onclick="selectClass()" required>

                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="section_id" class="form-label">Section <span class="text-danger">*</span></label>
                            <select name="section_id" id="student_section_id" class="form-select" required>

                            </select>    
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="roll_no" class="form-label">Roll No <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="roll_no" name="roll_no">  
                        </div> --}}

                        
                        <div class="col-auto">
                            <button class="btn btn-success px-4" type="submit">
                                <i class="fa fa-save me-1"></i>Submit
                            </button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-secondary px-4" type="reset">
                                <i class="fa fa-undo me-1"></i> Reset
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        {{-- <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="subject_name_search" placeholder="Search by Name...">
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control" id="subject_code_search" placeholder="Search by Code...">
            </div>
            <div class="col-md-2">
                <select name="type" id="subject_type_search" class="form-select">
                    <option value="">Select Type</option>
                    <option value="theory">Theory</option>
                    <option value="practical">Practical</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" id="subject_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div> --}}
    </div>
@endsection

@push('scripts')
    <script>
        // Get classes
        function getClasses(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.classes') }}",
                success: function (response) {
                    $('#student_class_id').html(response.html);
                    $('#edit_student_class_id').html(response.html);
                }
            });
        }
        getClasses();

        // Get sections
        function selectClass(){
            const class_id = $('#student_class_id').val();
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#student_section_id').html(response.html);
                }
            });
        }

        // Get sections for edit
        function selectClassForEdit(sectionId = null){
            const class_id = $('#edit_student_class_id').val();
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#edit_student_section_id').html(response.html);
                    if(sectionId != null){
                        $('#edit_student_section_id').val(sectionId);
                    }
                }
            });
        }
    </script>
@endpush