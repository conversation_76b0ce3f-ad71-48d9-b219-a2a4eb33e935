@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->user->name ?? '' }}</td>
        <td>{{ $row->user->email ?? '' }}</td>
        <td>{{ $row->parent_contact_no ?? '' }}</td>
        <td>{{ $row->user->address ?? '' }}</td>
        <td class="text-center">
            @if ($row->user->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->user->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->user->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <a href="{{ route('students.show',$row->id) }}" class="btn btn-sm btn-outline-info" title="View Student Profile">
                <i class="fa-solid fa-eye"></i>
            </a>
            <a href="{{ route('students.edit',$row->id) }}" class="btn btn-sm btn-outline-warning" title="Edit Student Profile">
                <i class="fa-solid fa-pen-to-square"></i>
            </a>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Section" onclick="deleteStudent({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No Students found.</td>
    </tr>
@endforelse