@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Students')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Students Management</h4>
            <a class="btn btn-primary" href="{{ route('students.create') }}">
                <i class="fas fa-plus"></i> Add Student
            </a>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="st_name_search" placeholder="Name...">
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control" id="st_parent_email_search" placeholder="Email...">
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control" id="st_parent_co_no" placeholder="Parent Contact No ...">
            </div>
            <div class="col-md-2">
                <select name="status" id="student_status" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>


        <!-- Students Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Parent Contact No</th>
                        <th class="text-center">Address</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.students.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function search(){
            const name = $('#st_name_search').val();
            const email = $('#st_parent_email_search').val();
            const phone = $('#st_parent_co_no').val();
            const status = $('#student_status').val();

            $.ajax({
                type: "GET",
                url: "{{ route('students.index') }}",
                data: {
                    'name': name,
                    'email': email,
                    'phone': phone,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#st_name_search').val('');
            $('#st_parent_email_search').val('');
            $('#st_parent_co_no').val('');
            $('#student_status').val('active');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const name = $('#st_name_search').val();
            const email = $('#st_parent_email_search').val();
            const phone = $('#st_parent_co_no').val();
            const status = $('#student_status').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'name': name,
                    'email': email,
                    'phone': phone,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>
    <x-delete-confirm functionName="deleteStudent" deleteUrl="students.destroy" />
@endpush
