@extends('layouts.admin')
@section('title','Student Profile')

@push('styles')
<style>
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.1);
        z-index: 1;
    }

    .profile-content {
        position: relative;
        z-index: 2;
    }

    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid rgba(255,255,255,0.3);
        object-fit: cover;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    }

    .info-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .info-item {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-value {
        font-weight: 500;
        color: #2c3e50;
        font-size: 1rem;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-active {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .status-inactive {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 1.5rem;
        position: relative;
        padding-left: 20px;
    }

    .section-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2px;
    }

    .action-btn {
        border-radius: 10px;
        padding: 10px 20px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .btn-edit {
        background: linear-gradient(45deg, #007bff, #0056b3);
        color: white;
    }

    .btn-edit:hover {
        background: linear-gradient(45deg, #0056b3, #004085);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,123,255,0.4);
    }

    .btn-back {
        background: linear-gradient(45deg, #6c757d, #495057);
        color: white;
    }

    .btn-back:hover {
        background: linear-gradient(45deg, #495057, #343a40);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108,117,125,0.4);
    }

    @media (max-width: 768px) {
        .profile-avatar {
            width: 100px;
            height: 100px;
        }

        .section-title {
            font-size: 1.1rem;
        }

        .action-btn {
            padding: 8px 16px;
            font-size: 0.9rem;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <x-alert />

    <!-- Profile Header -->
    <div class="profile-header p-4 mb-4">
        <div class="profile-content">
            <div class="row align-items-center">
                <div class="col-auto">
                    <img src="{{ $student->user->photo ? asset('storage/'.$student->user->photo) : asset('assets/images/default-avatar.png') }}"
                         alt="Student Photo"
                         class="profile-avatar">
                </div>
                <div class="col">
                    <h1 class="h2 mb-2">{{ $student->user->name }}</h1>
                    <p class="mb-2 opacity-75">
                        <i class="fas fa-envelope me-2"></i>{{ $student->user->email }}
                    </p>
                    <p class="mb-2 opacity-75">
                        <i class="fas fa-phone me-2"></i>{{ $student->parent_contact_no }}
                    </p>
                    <span class="status-badge {{ $student->user->status == 'active' ? 'status-active' : 'status-inactive' }}">
                        {{ ucfirst($student->user->status) }}
                    </span>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ route('students.edit', $student->id) }}" class="btn action-btn btn-edit">
                            <i class="fas fa-edit me-2"></i>Edit Profile
                        </a>
                        <a href="{{ route('students.index') }}" class="btn action-btn btn-back">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="row g-4">
        <!-- Personal Information -->
        <div class="col-lg-6">
            <div class="card info-card h-100">
                <div class="card-body p-4">
                    <h5 class="section-title">Personal Information</h5>

                    <div class="info-item">
                        <div class="info-label">Full Name</div>
                        <div class="info-value">{{ $student->user->name }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Date of Birth</div>
                        <div class="info-value">
                            <i class="fas fa-calendar-alt me-2 text-primary"></i>
                            {{ $student->dob }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Gender</div>
                        <div class="info-value">
                            <i class="fas fa-{{ $student->gender == 'male' ? 'mars' : 'venus' }} me-2 text-primary"></i>
                            {{ ucfirst($student->gender) }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Blood Group</div>
                        <div class="info-value">
                            <i class="fas fa-tint me-2 text-danger"></i>
                            {{ $student->blood_group ?? 'Not specified' }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Caste</div>
                        <div class="info-value">{{ $student->caste }}</div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Address</div>
                        <div class="info-value">
                            <i class="fas fa-map-marker-alt me-2 text-success"></i>
                            {{ $student->user->address }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Family Information -->
        <div class="col-lg-6">
            <div class="card info-card h-100">
                <div class="card-body p-4">
                    <h5 class="section-title">Family Information</h5>

                    <div class="info-item">
                        <div class="info-label">Father's Name</div>
                        <div class="info-value">
                            <i class="fas fa-user me-2 text-primary"></i>
                            {{ $student->father_name }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Mother's Name</div>
                        <div class="info-value">
                            <i class="fas fa-user me-2 text-pink"></i>
                            {{ $student->mother_name }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Father's Occupation</div>
                        <div class="info-value">
                            <i class="fas fa-briefcase me-2 text-warning"></i>
                            {{ $student->father_occupation ?? 'Not specified' }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Parent Contact</div>
                        <div class="info-value">
                            <i class="fas fa-phone me-2 text-success"></i>
                            <a href="tel:{{ $student->parent_contact_no }}" class="text-decoration-none">
                                {{ $student->parent_contact_no }}
                            </a>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Email Address</div>
                        <div class="info-value">
                            <i class="fas fa-envelope me-2 text-info"></i>
                            <a href="mailto:{{ $student->user->email }}" class="text-decoration-none">
                                {{ $student->user->email }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Academic Information -->
        <div class="col-lg-6">
            <div class="card info-card h-100">
                <div class="card-body p-4">
                    <h5 class="section-title">Academic Information</h5>

                    <div class="info-item">
                        <div class="info-label">Student ID</div>
                        <div class="info-value">
                            <span class="badge bg-primary">STU-{{ str_pad($student->id, 4, '0', STR_PAD_LEFT) }}</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Roll Number</div>
                        <div class="info-value">
                            <i class="fas fa-hashtag me-2 text-primary"></i>
                            {{ $student->roll_no ?? 'Not assigned' }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Class</div>
                        <div class="info-value">
                            <i class="fas fa-school me-2 text-success"></i>
                            {{ $student->class_id ? 'Class ' . $student->class_id : 'Not assigned' }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Section</div>
                        <div class="info-value">
                            <i class="fas fa-users me-2 text-info"></i>
                            {{ $student->section_id ? 'Section ' . $student->section_id : 'Not assigned' }}
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-label">Admission Date</div>
                        <div class="info-value">
                            <i class="fas fa-calendar-check me-2 text-warning"></i>
                            {{ $student->admission_date ? date('d-m-Y', strtotime($student->admission_date)) : 'Not specified' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-lg-6">
            <div class="card info-card h-100">
                <div class="card-body p-4">
                    <h5 class="section-title">Quick Statistics</h5>

                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                                <h4 class="mb-1 text-success">95%</h4>
                                <small class="text-muted">Attendance</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-star fa-2x text-warning mb-2"></i>
                                <h4 class="mb-1 text-warning">A+</h4>
                                <small class="text-muted">Grade</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-book fa-2x text-info mb-2"></i>
                                <h4 class="mb-1 text-info">8</h4>
                                <small class="text-muted">Subjects</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-trophy fa-2x text-primary mb-2"></i>
                                <h4 class="mb-1 text-primary">3</h4>
                                <small class="text-muted">Awards</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection