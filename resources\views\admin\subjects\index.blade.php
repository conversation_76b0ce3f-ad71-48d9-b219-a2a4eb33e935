@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Subjects')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Subjects Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                <i class="fas fa-plus"></i> Add Subject
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="subject_name_search" placeholder="Search by Name...">
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control" id="subject_code_search" placeholder="Search by Code...">
            </div>
            <div class="col-md-2">
                <select name="type" id="subject_type_search" class="form-select">
                    <option value="">Select Type</option>
                    <option value="theory">Theory</option>
                    <option value="practical">Practical</option>
                </select>
            </div>
            <div class="col-md-2">
                <select name="status" id="subject_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>


        <!-- Subjects Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="ClassesTable">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Code</th>
                        <th>Type</th>
                        <th class="text-center">Created At</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.subjects.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Edit Subject Modal -->
    <div class="modal fade" id="editSuubjectModal" tabindex="-1" aria-labelledby="editSectionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editSectionModalLabel">Update Subject</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_subject_id">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_subject_name" name="name">
                    </div>
                    <div class="mb-3">
                        <label for="code" class="form-label">Code <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_subject_code" name="code">
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Type <span class="text-danger">*</span></label>
                        <select name="status" id="edit_subject_type" class="form-select">
                            <option value="theory">Theory</option>
                            <option value="practical">Practical</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_subject_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateSubject()">Update Subject</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function search(){
            const name = $('#subject_name_search').val();
            const code = $('#subject_code_search').val();
            const type = $('#subject_type_search').val();
            const status = $('#subject_status_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('subjects.index') }}",
                data: {
                    'name': name,
                    'code': code,
                    'type': type,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#subject_name_search').val('');
            $('#subject_code_search').val('');
            $('#subject_type_search').val('theory');
            $('#subject_status_search').val('active');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const name = $('#subject_name_search').val();
            const code = $('#subject_code_search').val();
            const type = $('#subject_type_search').val();
            const status = $('#subject_status_search').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'name': name,
                    'code': code,
                    'type': type,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        // Edit 
        function editSubject(id){
            $('#edit_subject_id').val(id);
            $('#edit_subject_code').val($('#edit_subject_code_data'+id).val());
            $('#edit_subject_name').val($('#edit_subject_name_data'+id).val());
            $('#edit_subject_type').val($('#edit_subject_type_data'+id).val());
            $('#edit_subject_status').val($('#edit_subject_status_data'+id).val());
            $('#editSuubjectModal').modal('show');
        }

        // Update
        function updateSubject(){
            const id = $('#edit_subject_id').val();
            const name = $('#edit_subject_name').val();
            const code = $('#edit_subject_code').val();
            const type = $('#edit_subject_type').val();
            const status = $('#edit_subject_status').val();

            if(name == '' || code == '' || type == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('subjects.update', ':id') }}".replace(':id', id),
                data: {
                    'code': code,
                    'name': name,
                    'type': type,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editSuubjectModal').modal('hide');
                        $('#edit_subject_code').val('');
                        $('#edit_subject_name').val('');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        let firstError = Object.values(errors)[0][0]; // first error message

                        alertify.alert()
                            .setting({
                                title: 'Validation Error',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                            }).show();
                    } else {
                        alertify.alert()
                            .setting({
                                title: 'Server Error',
                                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                            }).show();
                    }
                }
            });
        }
    </script>

    @include('admin.subjects.add-modal')
    <x-delete-confirm functionName="deleteSubject" deleteUrl="subjects.destroy" />
@endpush
