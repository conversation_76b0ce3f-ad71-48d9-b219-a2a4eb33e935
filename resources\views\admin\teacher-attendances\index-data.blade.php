@forelse($records as $row)
    <tr>
        <td>
            <div class="fw-bold text-primary">{{ $loop->iteration }}</div>
        </td>
        <td>
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <img src="{{ $row->user->photo ? asset('storage/'.$row->user->photo) : asset('assets/images/default-avatar.png') }}" 
                         alt="Teacher" 
                         class="rounded-circle" 
                         width="40" 
                         height="40"
                         style="object-fit: cover;">
                </div>
                <div>
                    <div class="fw-semibold text-dark">{{ $row->user->name ?? '' }}</div>
                    <small class="text-muted">
                        <i class="fas fa-id-badge me-1"></i>{{ $row->emp_code ?? 'N/A' }}
                    </small>
                </div>
            </div>
        </td>
        <td>
            <div class="fw-semibold">{{ date('d-m-Y') }}</div>
            <small class="text-muted">{{ date('l') }}</small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-attendance btn-present" 
                        title="Mark Present" 
                        onclick="presentAttendance({{ $row->id }})"
                        {{ isset($row->teacherAttendance->status) ? 'disabled' : '' }}
                        id="present_btn_{{ $row->id }}">
                    <i class="fas fa-check me-1"></i>Present
                </button>
                <button class="btn btn-attendance btn-absent" 
                        title="Mark Absent" 
                        onclick="absentAttendance({{ $row->id }})"
                        {{ isset($row->teacherAttendance->status) ? 'disabled' : '' }}
                        id="absent_btn_{{ $row->id }}">
                    <i class="fas fa-times me-1"></i>Absent
                </button>
                <button class="btn btn-attendance btn-leave" 
                        title="Mark On Leave" 
                        onclick="leaveAttendance({{ $row->id }})"
                        {{ isset($row->teacherAttendance->status) ? 'disabled' : '' }}
                        id="leave_btn_{{ $row->id }}">
                    <i class="fas fa-calendar-times me-1"></i>Leave
                </button>
            </div>
        </td>
        <td id="check_time_{{ $row->id }}">
            @if($row->teacherAttendance->check_in_time ?? null)
                <div class="fw-semibold text-success">
                    <i class="fas fa-sign-in-alt me-1"></i>{{ $row->teacherAttendance->check_in_time }}
                </div>
                @if($row->teacherAttendance->check_out_time ?? null)
                    <div class="fw-semibold text-danger">
                        <i class="fas fa-sign-out-alt me-1"></i>{{ $row->teacherAttendance->check_out_time }}
                    </div>
                @endif
            @else
                <span class="text-muted">-</span>
            @endif
        </td>
        <td id="working_hours_{{ $row->id }}">
            @if($row->teacherAttendance->working_hours ?? null)
                <div class="fw-semibold text-info">
                    <i class="fas fa-clock me-1"></i>{{ $row->teacherAttendance->working_hours }} hrs
                </div>
            @else
                <span class="text-muted">-</span>
            @endif
        </td>
        <td class="text-center" id="status_{{ $row->id }}">
            <span class="status-badge status-present" 
                  id="status_present_{{ $row->id }}" 
                  style="{{ isset($row->teacherAttendance->status) && $row->teacherAttendance->status == 'present' ? '' : 'display:none' }}">
                <i class="fas fa-check me-1"></i>Present
            </span>
            
            <span class="status-badge status-absent" 
                  id="status_absent_{{ $row->id }}" 
                  style="{{ isset($row->teacherAttendance->status) && $row->teacherAttendance->status == 'absent' ? '' : 'display:none' }}">
                <i class="fas fa-times me-1"></i>Absent
            </span>
            
            <span class="status-badge status-on-leave" 
                  id="status_leave_{{ $row->id }}" 
                  style="{{ isset($row->teacherAttendance->status) && $row->teacherAttendance->status == 'on_leave' ? '' : 'display:none' }}">
                <i class="fas fa-calendar-times me-1"></i>On Leave
            </span>
            
            <span class="status-badge status-half-day" 
                  id="status_half_day_{{ $row->id }}" 
                  style="{{ isset($row->teacherAttendance->status) && $row->teacherAttendance->status == 'half_day' ? '' : 'display:none' }}">
                <i class="fas fa-clock me-1"></i>Half Day
            </span>
            
            <span class="status-badge status-not-marked" 
                  id="status_not_marked_{{ $row->id }}" 
                  style="{{ isset($row->teacherAttendance->status) ? 'display:none' : '' }}">
                <i class="fas fa-question me-1"></i>Not Marked
            </span>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-chalkboard-teacher fa-3x mb-3"></i>
                <h5>No Teacher Attendances Found</h5>
                <p>No teacher attendance records available for the selected criteria.</p>
            </div>
        </td>
    </tr>
@endforelse
