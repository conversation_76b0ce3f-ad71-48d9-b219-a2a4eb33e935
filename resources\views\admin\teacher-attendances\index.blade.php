@extends('layouts.admin')
@section('title', 'Teacher Attendances')

@push('styles')
<style>
    body {
        background-color: #f8f9fa;
    }

    .attendance-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 20px 0;
    }

    .attendance-header {
        background: white;
        padding: 20px 24px;
        border-bottom: 1px solid #e9ecef;
    }

    .attendance-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .attendance-subtitle {
        font-size: 14px;
        color: #6c757d;
        margin: 4px 0 0 0;
    }

    .search-filters {
        padding: 20px 24px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .search-input {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 10px 16px;
        font-size: 14px;
        background: white;
        transition: all 0.3s ease;
        width: 100%;
    }

    .search-input:focus {
        border-color: #4285f4;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        outline: none;
    }

    .search-input::placeholder {
        color: #9ca3af;
    }

    .btn-search {
        background: #4285f4;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-search:hover {
        background: #3367d6;
        color: white;
    }

    .btn-report {
        background: #34a853;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 10px;
    }

    .btn-report:hover {
        background: #2d8f47;
        color: white;
    }

    .attendance-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .attendance-table thead th {
        background: #f8f9fa;
        color: #5f6368;
        font-weight: 500;
        font-size: 14px;
        padding: 16px 20px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }

    .attendance-table tbody td {
        padding: 16px 20px;
        border-bottom: 1px solid #f1f3f4;
        font-size: 14px;
        color: #3c4043;
        vertical-align: middle;
    }

    .attendance-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .member-info {
        display: flex;
        align-items: center;
    }

    .member-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #e8f0fe;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 14px;
        color: #4285f4;
        font-weight: 500;
    }

    .member-name {
        font-weight: 500;
        color: #3c4043;
    }

    .member-phone {
        color: #5f6368;
        font-size: 13px;
    }

    .check-in-btn {
        background: #4285f4;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .check-in-btn:hover {
        background: #3367d6;
    }

    .check-in-btn:disabled {
        background: #e8eaed;
        color: #9aa0a6;
        cursor: not-allowed;
    }

    .status-present {
        color: #34a853;
        font-weight: 500;
    }

    .status-absent {
        color: #ea4335;
        font-weight: 500;
    }

    .status-on-leave {
        color: #fbbc04;
        font-weight: 500;
    }

    .status-half-day {
        color: #4285f4;
        font-weight: 500;
    }

    .attendance-actions {
        display: flex;
        gap: 8px;
    }

    .btn-present {
        background: #34a853;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-present:hover {
        background: #2d8f47;
    }

    .btn-absent {
        background: #ea4335;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-absent:hover {
        background: #d33b2c;
    }

    .btn-leave {
        background: #fbbc04;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-leave:hover {
        background: #f9ab00;
    }

    .btn-present:disabled,
    .btn-absent:disabled,
    .btn-leave:disabled {
        background: #e8eaed;
        color: #9aa0a6;
        cursor: not-allowed;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #5f6368;
    }

    .empty-state i {
        font-size: 48px;
        color: #dadce0;
        margin-bottom: 16px;
    }

    .empty-state h5 {
        color: #3c4043;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .pagination-container {
        padding: 20px 24px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    @media (max-width: 768px) {
        .attendance-container {
            margin: 10px;
            border-radius: 8px;
        }

        .attendance-header,
        .search-filters {
            padding: 16px;
        }

        .attendance-table thead th,
        .attendance-table tbody td {
            padding: 12px 16px;
        }

        .member-avatar {
            width: 28px;
            height: 28px;
            font-size: 12px;
        }

        .attendance-actions {
            flex-direction: column;
            gap: 4px;
        }
    }
</style>
@endpush

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-present {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .status-absent {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .status-on-leave {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }

    .status-half-day {
        background: linear-gradient(45deg, #17a2b8, #20c997);
        color: white;
    }

    .btn-attendance {
        padding: 0.4rem 0.8rem;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        border: none;
        margin: 0 0.2rem;
        transition: all 0.3s ease;
    }

    .btn-present {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .btn-present:hover {
        background: linear-gradient(45deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-1px);
    }

    .btn-absent {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .btn-absent:hover {
        background: linear-gradient(45deg, #fd7e14, #ffc107);
        color: white;
        transform: translateY(-1px);
    }

    .btn-leave {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }

    .btn-leave:hover {
        background: linear-gradient(45deg, #fd7e14, #dc3545);
        color: white;
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .page-header-content {
            padding: 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .filter-body {
            padding: 1rem;
        }

        .btn-modern {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <x-alert />

    <!-- Attendance Container -->
    <div class="attendance-container">
        <!-- Header -->
        <div class="attendance-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="attendance-title">
                        <i class="fas fa-chalkboard-teacher me-2"></i>Teacher Attendance
                    </h1>
                    <p class="attendance-subtitle">Manage teacher attendance records</p>
                </div>
                <button type="button" class="btn-report">
                    <i class="fas fa-chart-bar me-2"></i>Attendance Report
                </button>
            </div>
        </div>

        <!-- Search Filters -->
        <div class="search-filters">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <input type="text"
                           class="search-input"
                           id="teacher_name"
                           placeholder="teacher first name...">
                </div>
                <div class="col-md-3">
                    <input type="text"
                           class="search-input"
                           id="teacher_phone"
                           placeholder="teacher phone number...">
                </div>
                <div class="col-auto">
                    <button type="button" class="btn-search" onclick="search()">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th>SN.</th>
                        <th>Teacher Name</th>
                        <th>Teacher Phone</th>
                        <th>Date</th>
                        <th>Check In</th>
                        <th>Check Out</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.teacher-attendances.index-data')
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" id="pagination_links">
            {{ $records->links('pagination::bootstrap-5') }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function search(){
        const teacher_name = $('#teacher_name').val();
        const teacher_phone = $('#teacher_phone').val();

        $.ajax({
            type: "GET",
            url: "{{ route('teacher-attendances.index') }}",
            data: {
                'teacher_name': teacher_name,
                'teacher_phone': teacher_phone
            },
            success: function (response) {
                $('#list_data').html(response.html);
                $('#pagination_links').html(response.pagination);
            }
        });
    }

    // Reset search form
    function resetSearchForm(){
        $('#teacher_name').val('');
        $('#teacher_phone').val('');
        search();
    }

    // Handle pagination link clicks
    $(document).on('click', '#pagination_links a', function(e){
        e.preventDefault();
        let url = $(this).attr('href');
        const teacher_name = $('#teacher_name').val();
        const teacher_phone = $('#teacher_phone').val();

        $.ajax({
            url: url,
            type: "GET",
            data: {
                'teacher_name': teacher_name,
                'teacher_phone': teacher_phone
            },
            success: function(response) {
                $('#list_data').html(response.html);
                $('#pagination_links').html(response.pagination);
            }
        });
    });

    // Mark Present Attendance
    function presentAttendance(id){
        $.ajax({
            type: "POST",
            url: "{{ route('teacher-attendances.mark') }}",
            data: {
                'teacher_id': id,
                'status': 'present',
                '_token': '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response.status == true){
                    // Update check-in column
                    $('#check_in_' + id).html(response.check_in_time || 'Present');

                    // Update status column
                    $('#status_' + id).html('<span class="status-present">Present</span>');

                    // Show success message
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
        });
    }

    // Mark Absent Attendance
    function absentAttendance(id){
        $.ajax({
            type: "POST",
            url: "{{ route('teacher-attendances.mark') }}",
            data: {
                'teacher_id': id,
                'status': 'absent',
                '_token': '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response.status == true){
                    // Update check-in column
                    $('#check_in_' + id).html('-');

                    // Update status column
                    $('#status_' + id).html('<span class="status-absent">Absent</span>');

                    // Show success message
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
        });
    }

    // Mark On Leave
    function leaveAttendance(id){
        $.ajax({
            type: "POST",
            url: "{{ route('teacher-attendances.mark') }}",
            data: {
                'teacher_id': id,
                'status': 'on_leave',
                '_token': '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response.status == true){
                    // Update check-in column
                    $('#check_in_' + id).html('-');

                    // Update status column
                    $('#status_' + id).html('<span class="status-on-leave">On Leave</span>');

                    // Show success message
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
        });
    }
</script>
@endpush
