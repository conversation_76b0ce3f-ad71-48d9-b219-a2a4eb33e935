@extends('layouts.admin')
@section('title', 'Teacher Attendances')

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.1);
        z-index: 1;
    }

    .page-header-content {
        position: relative;
        z-index: 2;
        padding: 2rem;
    }

    .page-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        opacity: 0.9;
        font-size: 1rem;
        margin-bottom: 0;
    }

    .filter-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .filter-header {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .filter-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }

    .filter-body {
        padding: 1.5rem;
    }

    .form-select, .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.8);
    }

    .form-select:focus, .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.15);
        background: rgba(255, 255, 255, 1);
        transform: translateY(-1px);
    }

    .btn-modern {
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-search {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .btn-search:hover {
        background: linear-gradient(45deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }

    .btn-reset {
        background: linear-gradient(45deg, #6c757d, #495057);
        color: white;
    }

    .btn-reset:hover {
        background: linear-gradient(45deg, #495057, #343a40);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }

    .data-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: none;
        overflow: hidden;
    }

    .table-modern {
        margin-bottom: 0;
    }

    .table-modern thead th {
        background: linear-gradient(45deg, #343a40, #495057);
        color: white;
        border: none;
        padding: 1rem 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.85rem;
    }

    .table-modern tbody td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #f8f9fa;
        vertical-align: middle;
    }

    .table-modern tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-present {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .status-absent {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .status-on-leave {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }

    .status-half-day {
        background: linear-gradient(45deg, #17a2b8, #20c997);
        color: white;
    }

    .btn-attendance {
        padding: 0.4rem 0.8rem;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        border: none;
        margin: 0 0.2rem;
        transition: all 0.3s ease;
    }

    .btn-present {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .btn-present:hover {
        background: linear-gradient(45deg, #20c997, #17a2b8);
        color: white;
        transform: translateY(-1px);
    }

    .btn-absent {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        color: white;
    }

    .btn-absent:hover {
        background: linear-gradient(45deg, #fd7e14, #ffc107);
        color: white;
        transform: translateY(-1px);
    }

    .btn-leave {
        background: linear-gradient(45deg, #ffc107, #fd7e14);
        color: white;
    }

    .btn-leave:hover {
        background: linear-gradient(45deg, #fd7e14, #dc3545);
        color: white;
        transform: translateY(-1px);
    }

    @media (max-width: 768px) {
        .page-header-content {
            padding: 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .filter-body {
            padding: 1rem;
        }

        .btn-modern {
            padding: 0.6rem 1.2rem;
            font-size: 0.9rem;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <x-alert />

    <!-- Page Header -->
    <div class="page-header">
        <div class="page-header-content">
            <h1 class="page-title">
                <i class="fas fa-chalkboard-teacher me-3"></i>Teacher Attendances
            </h1>
            <p class="page-subtitle">Manage and track teacher attendance records</p>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="card filter-card">
        <div class="filter-header">
            <h5 class="filter-title">
                <i class="fas fa-filter me-2"></i>Filter Options
            </h5>
        </div>
        <div class="filter-body">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-user me-1"></i>Teacher Name
                    </label>
                    <input type="text" id="teacher_name" class="form-control" placeholder="Search by name...">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-calendar me-1"></i>Date
                    </label>
                    <input type="date" id="attendance_date" class="form-control" value="{{ date('Y-m-d') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-info-circle me-1"></i>Status
                    </label>
                    <select id="status_filter" class="form-select">
                        <option value="">All Status</option>
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="on_leave">On Leave</option>
                        <option value="half_day">Half Day</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-modern btn-search" onclick="search()">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn btn-modern btn-reset" onclick="resetSearchForm()">
                        <i class="fas fa-undo me-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Card -->
    <div class="card data-card">
        <div class="table-responsive">
            <table class="table table-modern" id="TeacherAttendanceTable">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="25%">Teacher</th>
                        <th width="12%">Date</th>
                        <th width="20%">Mark Attendance</th>
                        <th width="15%">Check In/Out</th>
                        <th width="13%">Working Hours</th>
                        <th width="10%">Status</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.teacher-attendances.index-data')
                </tbody>
            </table>
        </div>
        <div class="p-3" id="pagination_links">
            {{ $records->links('pagination::bootstrap-5') }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function search(){
        const teacher_name = $('#teacher_name').val();
        const attendance_date = $('#attendance_date').val();
        const status_filter = $('#status_filter').val();

        $.ajax({
            type: "GET",
            url: "{{ route('teacher-attendances.index') }}",
            data: {
                'teacher_name': teacher_name,
                'attendance_date': attendance_date,
                'status_filter': status_filter
            },
            success: function (response) {
                $('#list_data').html(response.html);
                $('#pagination_links').html(response.pagination);
            }
        });
    }

    // Reset search form
    function resetSearchForm(){
        $('#teacher_name').val('');
        $('#attendance_date').val('{{ date('Y-m-d') }}');
        $('#status_filter').val('');
        search();
    }

    // Handle pagination link clicks
    $(document).on('click', '#pagination_links a', function(e){
        e.preventDefault();
        let url = $(this).attr('href');
        const teacher_name = $('#teacher_name').val();
        const attendance_date = $('#attendance_date').val();
        const status_filter = $('#status_filter').val();

        $.ajax({
            url: url,
            type: "GET",
            data: {
                'teacher_name': teacher_name,
                'attendance_date': attendance_date,
                'status_filter': status_filter
            },
            success: function(response) {
                $('#list_data').html(response.html);
                $('#pagination_links').html(response.pagination);
            }
        });
    });

    // Mark Present Attendance
    function presentAttendance(id){
        $.ajax({
            type: "POST",
            url: "{{ route('teacher-attendances.mark') }}",
            data: {
                'teacher_id': id,
                'status': 'present',
                '_token': '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response.status == true){
                    updateAttendanceUI(id, 'present', response);
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
        });
    }

    // Mark Absent Attendance
    function absentAttendance(id){
        $.ajax({
            type: "POST",
            url: "{{ route('teacher-attendances.mark') }}",
            data: {
                'teacher_id': id,
                'status': 'absent',
                '_token': '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response.status == true){
                    updateAttendanceUI(id, 'absent', response);
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
        });
    }

    // Mark On Leave
    function leaveAttendance(id){
        $.ajax({
            type: "POST",
            url: "{{ route('teacher-attendances.mark') }}",
            data: {
                'teacher_id': id,
                'status': 'on_leave',
                '_token': '{{ csrf_token() }}'
            },
            success: function (response) {
                if(response.status == true){
                    updateAttendanceUI(id, 'on_leave', response);
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
        });
    }

    // Update UI after marking attendance
    function updateAttendanceUI(id, status, response) {
        // Disable all buttons
        $('#present_btn_' + id).attr('disabled', true);
        $('#absent_btn_' + id).attr('disabled', true);
        $('#leave_btn_' + id).attr('disabled', true);

        // Update check in/out time
        $('#check_time_' + id).html(response.check_in_time || response.check_out_time || '-');

        // Update working hours
        $('#working_hours_' + id).html(response.working_hours || '-');

        // Hide all status badges
        $('#status_present_' + id).hide();
        $('#status_absent_' + id).hide();
        $('#status_leave_' + id).hide();
        $('#status_not_marked_' + id).hide();

        // Show appropriate status badge
        $('#status_' + status.replace('_', '_') + '_' + id).show();
    }
</script>
@endpush
