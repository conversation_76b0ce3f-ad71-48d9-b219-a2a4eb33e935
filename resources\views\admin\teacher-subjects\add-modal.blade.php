<!-- Add Teacher Subject Modal -->
<div class="modal fade" id="addTeacherSubjectModal" tabindex="-1" aria-labelledby="addTeacherSubjectModalLabel">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addTeacherSubjectModalLabel">Add Teacher Subject</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                    <select name="class_id" id="teacher_subject_class_id" class="form-select" onclick="selectClass()">
                        
                    </select>
                </div>
                <div class="mb-3">  
                    <label for="section_id" class="form-label">Section <span class="text-danger">*</span>
                        <button class="btn btn-sm btn-circle btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </label>
                    <select name="section_id" id="teacher_subject_section_id" class="form-select" required>

                    </select>
                </div>
                <div class="mb-3">
                    <label for="subject_id" class="form-label">Subject <span class="text-danger">*</span>
                        <button class="btn btn-sm btn-circle btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </label>
                    <select name="subject_id" id="teacher_subject_subject_id" class="form-select" required>

                    </select>
                </div>
                <div class="mb-3">
                    <label for="teacher_id" class="form-label">Teacher <span class="text-danger">*</span>
                        <button class="btn btn-sm btn-circle btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </label>
                    <select name="teacher_id" id="teacher_subject_teacher_id" class="form-select" required>

                    </select>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="teacher_subject_status" class="form-select" required>
                        <option value="active" @selected(old('status') == "active")>Active</option>
                        <option value="inactive" @selected(old('status') == "inactive")>Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addTeacherSubject()">Save Teacher Subject</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store    
    function addTeacherSubject(){
        const class_id = $('#teacher_subject_class_id').val();
        const subject_id = $('#teacher_subject_subject_id').val();    
        const teacher_id = $('#teacher_subject_teacher_id').val();    
        const section_id = $('#teacher_subject_section_id').val();    
        const status = $('#teacher_subject_status').val();

        if(class_id == '' || subject_id == '' || teacher_id == '' || section_id == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('teacher-subjects.store') }}",
            data: {
                'class_id': class_id,   
                'subject_id': subject_id,
                'teacher_id': teacher_id,
                'section_id': section_id,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addTeacherSubjectModal').modal('hide');
                    $('#teacher_subject_class_id').val('');
                    $('#teacher_subject_subject_id').val('');
                    $('#teacher_subject_teacher_id').val('');
                    $('#teacher_subject_section_id').val('');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else if (xhr.status === 400) {
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + xhr.responseJSON.message
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>