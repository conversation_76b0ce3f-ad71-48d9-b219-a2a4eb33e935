@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->class->name ?? '' }}</td>
        <td>{{ $row->section->name ?? '' }} </td>
        <td>{{ $row->subject->name ?? '' }} ({{ $row->subject->code ?? '' }})</td>
        <td>{{ $row->teacher->user->name ?? '' }} ({{ $row->teacher->emp_code ?? '' }})</td>
        <td class="text-center">
            @if ($row->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Teacher Subject" onclick="editTeacherSubject({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_teacher_subject_class_id_data{{ $row->id }}" value="{{ $row->class->id ?? '' }}">
                <input type="hidden" id="edit_teacher_subject_subject_id_data{{ $row->id }}" value="{{ $row->subject->id ?? '' }}">
                <input type="hidden" id="edit_teacher_subject_teacher_id_data{{ $row->id }}" value="{{ $row->teacher->id ?? '' }}">
                <input type="hidden" id="edit_teacher_subject_section_id_data{{ $row->id }}" value="{{ $row->section->id ?? '' }}">
                <input type="hidden" id="edit_teacher_subject_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Teacher Subject" onclick="deleteTeacherSubject({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No Teacher Subjects found.</td>
    </tr>
@endforelse