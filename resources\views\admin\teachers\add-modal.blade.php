<!-- Add Teacher Modal -->
<div class="modal fade" id="addTeacherModal" tabindex="-1" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addTeacherModalLabel">Add New Teacher</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_name" name="name">
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="teacher_email" name="email">
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="teacher_password" name="password">
                </div>
                <div class="mb-3">
                    <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_phone" name="phone">
                </div>
                <div class="mb-3">
                    <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_address" name="address">
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="teacher_status" class="form-select">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addTeacher()">Save Teacher</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addTeacher(){
        const name = $('#teacher_name').val();
        const email = $('#teacher_email').val();
        const password = $('#teacher_password').val();
        const phone = $('#teacher_phone').val();
        const address = $('#teacher_address').val();
        const status = $('#teacher_status').val();

        if(name == '' || email == '' || password == '' || phone == '' || address == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('teachers.store') }}",
            data: {
                'name': name,
                'email': email,
                'password': password,
                'phone': phone,
                'address': address,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addTeacherModal').modal('hide');
                    $('#teacher_name').val('');
                    $('#teacher_email').val('');
                    $('#teacher_password').val('');
                    $('#teacher_phone').val('');
                    $('#teacher_address').val('');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>