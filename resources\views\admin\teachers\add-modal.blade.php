<!-- Add Teacher Modal -->
<div class="modal fade" id="addTeacherModal" tabindex="-1" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addTeacherModalLabel">Add New Teacher</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body row">

                <div class="mb-3 col-md-6">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_name" name="name">
                </div>
                <div class="mb-3 col-md-6">
                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                    <input type="email" class="form-control" id="teacher_email" name="email">
                </div>
                <div class="mb-3 col-md-6">
                    <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="teacher_password" name="password">
                </div>
                <div class="mb-3 col-md-6">
                    <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_phone" name="phone">
                </div>
                <div class="mb-3 col-md-6">
                    <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_address" name="address">
                </div>

                {{-- teacher table start --}}

                <div class="mb-3 col-md-6">
                    <label for="emp_code" class="form-label">Employee Code <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_emp_code" name="emp_code">
                </div>

                <div class="mb-3 col-md-6">
                    <label for="qualification" class="form-label">Qualification <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="teacher_qualification" name="qualification">
                </div>

                <div class="mb-3 col-md-6">
                    <label for="experience" class="form-label">Experience <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="teacher_experience" name="experience">
                </div>

                <div class="mb-3 col-md-6">
                    <label for="joining_date" class="form-label">Joining Date <span class="text-danger">*</span></label>
                    <input type="text" class="form-control datepicker" id="teacher_joining_date" name="joining_date" autocomplete="OFF" value="{{ date('d-m-Y') }}">
                </div>

                {{-- teacher table end --}}

                <div class="mb-3 col-md-6">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="teacher_status" class="form-select">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addTeacher()">Save Teacher</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store 
    function addTeacher(){
        const name = $('#teacher_name').val();
        const email = $('#teacher_email').val();
        const password = $('#teacher_password').val();
        const phone = $('#teacher_phone').val();
        const address = $('#teacher_address').val();
        const emp_code = $('#teacher_emp_code').val();
        const qualification = $('#teacher_qualification').val();
        const experience = $('#teacher_experience').val();
        const joining_date = $('#teacher_joining_date').val();
        const status = $('#teacher_status').val();

        if(name == '' || email == '' || password == '' || phone == '' || address == '' || status == '' || emp_code == '' || qualification == '' || experience == '' || joining_date == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: "All field is required!"
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('teachers.store') }}",
            data: {
                'name': name,
                'email': email,
                'password': password,
                'phone': phone,
                'address': address,
                'emp_code': emp_code,
                'qualification': qualification,
                'experience': experience,
                'joining_date': joining_date,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addTeacherModal').modal('hide');
                    $('#teacher_name').val('');
                    $('#teacher_email').val('');
                    $('#teacher_password').val('');
                    $('#teacher_phone').val('');
                    $('#teacher_address').val('');
                    $('#teacher_emp_code').val('');
                    $('#teacher_qualification').val('');
                    $('#teacher_experience').val('');
                    $('#teacher_joining_date').val({{ date('Y-m-d') }});
                    alertify.success(response.message);

                    // If teacher subject is true then open teacher subject modal
                    window.currentRouteName = "{{ Route::currentRouteName() }}";
                    if (window.currentRouteName && window.currentRouteName.startsWith('teacher-subjects')) {    
                        getTeachers();
                        $('#addTeacherSubjectModal').modal('show');
                    }

                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    let firstError = Object.values(errors)[0][0]; // first error message

                    alertify.alert()
                        .setting({
                            title: 'Validation Error',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                        }).show();
                } else {
                    alertify.alert()
                        .setting({
                            title: 'Server Error',
                            message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                        }).show();
                }
            }
        });

    }
</script>