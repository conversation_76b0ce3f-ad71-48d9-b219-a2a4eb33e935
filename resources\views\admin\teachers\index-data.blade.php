@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->name ?? '' }}</td>
        <td>{{ $row->email ?? '' }}</td>
        <td>{{ $row->phone ?? '' }}</td>
        <td>{{ $row->address ?? '' }}</td>
        <td  class="text-center">{{ $row->created_at->format('d M Y, h:i A') }}</td>
        <td class="text-center">
            @if ($row->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Teacher" onclick="editTeacher({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_teacher_name_data{{ $row->id }}" value="{{ $row->name ?? '' }}">
                <input type="hidden" id="edit_teacher_email_data{{ $row->id }}" value="{{ $row->email ?? '' }}">
                <input type="hidden" id="edit_teacher_phone_data{{ $row->id }}" value="{{ $row->phone ?? '' }}">
                <input type="hidden" id="edit_teacher_address_data{{ $row->id }}" value="{{ $row->address ?? '' }}">
                <input type="hidden" id="edit_teacher_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="8" class="text-center">No Teachers found.</td>
    </tr>
@endforelse