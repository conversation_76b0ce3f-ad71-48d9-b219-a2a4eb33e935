@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->teacher->emp_code ?? '' }}</td>
        <td>{{ $row->name ?? '' }}</td>
        <td>{{ $row->phone ?? '' }}</td>
        <td>{{ $row->teacher->joining_date ?? '' }}</td>
        <td class="text-center">
            @if ($row->status == 'active')
                <span class="badge bg-success">{{ ucfirst($row->status) }}</span>
            @else
                <span class="badge bg-danger">{{ ucfirst($row->status ?? '') }}</span>
            @endif
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-info" type="button" title="View Teacher" onclick="viewTeacher({{ $row->id }})">
                <i class="fa-solid fa-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-warning" title="Edit Teacher" onclick="editTeacher({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_teacher_name_data{{ $row->id }}" value="{{ $row->name ?? '' }}">
                <input type="hidden" id="edit_teacher_email_data{{ $row->id }}" value="{{ $row->email ?? '' }}">
                <input type="hidden" id="edit_teacher_phone_data{{ $row->id }}" value="{{ $row->phone ?? '' }}">
                <input type="hidden" id="edit_teacher_address_data{{ $row->id }}" value="{{ $row->address ?? '' }}">
                <input type="hidden" id="edit_teacher_status_data{{ $row->id }}" value="{{ $row->status }}">

                <input type="hidden" id="edit_teacher_id_data{{ $row->id }}" value="{{ $row->teacher->id ?? '' }}">
                <input type="hidden" id="edit_teacher_emp_code_data{{ $row->id }}" value="{{ $row->teacher->emp_code ?? '' }}">
                <input type="hidden" id="edit_teacher_qualification_data{{ $row->id }}" value="{{ $row->teacher->qualification ?? '' }}">
                <input type="hidden" id="edit_teacher_experience_data{{ $row->id }}" value="{{ $row->teacher->experience ?? '' }}">
                <input type="hidden" id="edit_teacher_joining_date_data{{ $row->id }}" value="{{ $row->teacher->joining_date ?? '' }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Teacher" onclick="deleteTeacher({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">No Teachers found.</td>
    </tr>
@endforelse