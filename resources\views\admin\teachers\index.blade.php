@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Teachers')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Teachers Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                <i class="fas fa-plus"></i> Add Teacher
            </button>
        </div>

        <!-- Teachers Table -->
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Address</th>
                        <th>Created At</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.teachers.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    {{-- Edit Teacher Modal --}}
    <div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editTeacherModalLabel">Update Teacher</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_teacher_id">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_name" name="name">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="edit_teacher_email" name="email">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_teacher_status" class="form-select">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateTeacher()">Update Teacher</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    @include('admin.teachers.add-modal')
@endsection

@push('scripts')
    <script>
        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            
            $.ajax({
                url: url,
                type: "GET",
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });

        // Search Filter
        function search(){
            $.ajax({
                type: "GET",
                url: "{{ route('teachers.index') }}",
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            search();
        }

        // Edit 
        function editTeacher(id){
            $('#edit_teacher_id').val(id);
            $('#edit_teacher_name').val($('#edit_teacher_name_data'+id).val());
            $('#edit_teacher_email').val($('#edit_teacher_email_data'+id).val());
            $('#edit_teacher_phone').val($('#edit_teacher_phone_data'+id).val());
            $('#edit_teacher_address').val($('#edit_teacher_address_data'+id).val());
            $('#edit_teacher_status').val($('#edit_teacher_status_data'+id).val());
            $('#editTeacherModal').modal('show');
        }

        // Update
        function updateTeacher(){
            const id = $('#edit_teacher_id').val();
            const name = $('#edit_teacher_name').val();
            const email = $('#edit_teacher_email').val();
            const phone = $('#edit_teacher_phone').val();
            const address = $('#edit_teacher_address').val();
            const status = $('#edit_teacher_status').val();

            if(name == '' || email == '' || phone == '' || address == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('teachers.update', ':id') }}".replace(':id', id),
                data: {
                    'name': name,
                    'email': email,
                    'phone': phone,
                    'address': address,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editTeacherModal').modal('hide');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }   
    </script>
    
@endpush