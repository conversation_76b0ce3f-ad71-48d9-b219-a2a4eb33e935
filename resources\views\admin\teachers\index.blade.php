@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Teachers')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Teachers Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                <i class="fas fa-plus"></i> Add Teacher
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="teacher_emp_code_search" placeholder="Search by Employee Code...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="teacher_name_search" placeholder="Search by Name...">
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control" id="teacher_phone_search" placeholder="Search by Phone...">
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control datepicker" id="teacher_joining_date_search" placeholder="Search by Joining Date...">
            </div>
            <div class="col-md-2">
                <select name="status" id="teacher_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>

        <!-- Teachers Table -->
        <div class="table-responsive">
            <table class="table table-bordered">
                        <th>#</th>
                        <th>Employee Code</th>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Joining Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.teachers.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    {{-- Edit Teacher Modal --}}
    <div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editTeacherModalLabel">Update Teacher</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body row">
                    <input type="hidden" id="edit_user_id">
                    <div class="mb-3 col-md-6">
                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_name" name="name">
                    </div>
                    <div class="mb-3 col-md-6">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="edit_teacher_email" name="email">
                    </div>
                    <div class="mb-3 col-md-6">
                        <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_phone" name="phone">
                    </div>
                    <div class="mb-3 col-md-6">
                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_address" name="address">
                    </div>
                    {{-- teacher table start --}}
                    <input type="hidden" id="edit_teacher_id" name="teacher_id">
                    <div class="mb-3 col-md-6">
                        <label for="emp_code" class="form-label">Employee Code <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_emp_code" name="emp_code">
                    </div>

                    <div class="mb-3 col-md-6">
                        <label for="qualification" class="form-label">Qualification <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_teacher_qualification" name="qualification">
                    </div>

                    <div class="mb-3 col-md-6">
                        <label for="experience" class="form-label">Experience <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="edit_teacher_experience" name="experience">
                    </div>

                    <div class="mb-3 col-md-6">
                        <label for="joining_date" class="form-label">Joining Date <span class="text-danger">*</span></label>
                        <input type="text" class="form-control datepicker" id="edit_teacher_joining_date" name="joining_date" autocomplete="OFF"> 
                    </div>

                    {{-- teacher table end --}}

                    <div class="mb-3 col-md-6">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_teacher_status" class="form-select">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateTeacher()">Update Teacher</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Teacher Detail Modal -->
    <div class="modal fade" id="viewTeacherModal" tabindex="-1" aria-labelledby="viewTeacherModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content shadow-sm">
        <!-- Header -->
        <div class="modal-header">
            <h5 class="modal-title" id="viewTeacherModalLabel">Teacher Detail</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <!-- Body -->
        <div class="modal-body p-3">
            <table class="table table-bordered mb-0">
            <tbody>
                <tr>
                <th class="w-25">Name :</th>
                <td id="view_teacher_name">—</td>
                </tr>
                <tr>
                <th>Email :</th>
                <td id="view_teacher_email">—</td>
                </tr>
                <tr>
                <th>Phone :</th>
                <td id="view_teacher_phone">—</td>
                </tr>
                <tr>
                <th>Address :</th>
                <td id="view_teacher_address">—</td>
                </tr>
                <tr>
                <th>Employee Code :</th>
                <td id="view_teacher_emp_code">—</td>
                </tr>
                <tr>
                <th>Qualification :</th>
                <td id="view_teacher_qualification">—</td>
                </tr>
                <tr>
                <th>Experience :</th>
                <td id="view_teacher_experience">—</td>
                </tr>
                <tr>
                <th>Joining Date :</th>
                <td id="view_teacher_joining_date">—</td>
                </tr>
            </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
        </div>
    </div>
    </div>

    @include('admin.teachers.add-modal')
@endsection

@push('scripts')
    <script>
        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const name = $('#teacher_name_search').val();
            const phone = $('#teacher_phone_search').val();
            const emp_code = $('#teacher_emp_code_search').val();
            const joining_date = $('#teacher_joining_date_search').val();
            const status = $('#teacher_status_search').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'name': name,
                    'phone': phone,
                    'emp_code': emp_code,
                    'joining_date': joining_date,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });

        // Search Filter
        function search(){
            const name = $('#teacher_name_search').val();
            const phone = $('#teacher_phone_search').val();
            const emp_code = $('#teacher_emp_code_search').val();
            const joining_date = $('#teacher_joining_date_search').val();
            const status = $('#teacher_status_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('teachers.index') }}",
                data: {
                    'name': name,
                    'phone': phone,
                    'emp_code': emp_code,
                    'joining_date': joining_date,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#teacher_name_search').val('');
            $('#teacher_phone_search').val('');
            $('#teacher_emp_code_search').val('');
            $('#teacher_joining_date_search').val('');
            $('#teacher_status_search').val('');
            search();
        }

        // View 
        function viewTeacher(id){
            $('#view_teacher_name').text($('#edit_teacher_name_data'+id).val());
            $('#view_teacher_email').text($('#edit_teacher_email_data'+id).val());
            $('#view_teacher_phone').text($('#edit_teacher_phone_data'+id).val());
            $('#view_teacher_address').text($('#edit_teacher_address_data'+id).val());
            $('#view_teacher_emp_code').text($('#edit_teacher_emp_code_data'+id).val());
            $('#view_teacher_qualification').text($('#edit_teacher_qualification_data'+id).val());
            $('#view_teacher_experience').text($('#edit_teacher_experience_data'+id).val());
            $('#view_teacher_joining_date').text($('#edit_teacher_joining_date_data'+id).val());
            $('#viewTeacherModal').modal('show');
        }

        // Edit 
        function editTeacher(id){
            $('#edit_user_id').val(id);
            $('#edit_teacher_name').val($('#edit_teacher_name_data'+id).val());
            $('#edit_teacher_email').val($('#edit_teacher_email_data'+id).val());
            $('#edit_teacher_phone').val($('#edit_teacher_phone_data'+id).val());
            $('#edit_teacher_address').val($('#edit_teacher_address_data'+id).val());
            $('#edit_teacher_status').val($('#edit_teacher_status_data'+id).val());

            $('#edit_teacher_id').val($('#edit_teacher_id_data'+id).val());
            $('#edit_teacher_emp_code').val($('#edit_teacher_emp_code_data'+id).val());
            $('#edit_teacher_qualification').val($('#edit_teacher_qualification_data'+id).val());
            $('#edit_teacher_experience').val($('#edit_teacher_experience_data'+id).val());
            $('#edit_teacher_joining_date').val($('#edit_teacher_joining_date_data'+id).val());

            $('#editTeacherModal').modal('show');
        }

        // Update
        function updateTeacher(){
            const id = $('#edit_user_id').val();
            const name = $('#edit_teacher_name').val();
            const email = $('#edit_teacher_email').val();
            const phone = $('#edit_teacher_phone').val();
            const address = $('#edit_teacher_address').val();
            const status = $('#edit_teacher_status').val();
            const teacher_id = $('#edit_teacher_id').val();
            const emp_code = $('#edit_teacher_emp_code').val();
            const qualification = $('#edit_teacher_qualification').val();
            const experience = $('#edit_teacher_experience').val();
            const joining_date = $('#edit_teacher_joining_date').val();

            if(name == '' || email == '' || phone == '' || address == '' || status == '' || emp_code == '' || qualification == '' || experience == '' || joining_date == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: "All field is required!"
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('teachers.update', ':id') }}".replace(':id', id),
                data: {
                    'name': name,
                    'email': email,
                    'phone': phone,
                    'address': address,
                    'status': status,
                    'teacher_id': teacher_id,
                    'emp_code': emp_code,
                    'qualification': qualification,
                    'experience': experience,
                    'joining_date': joining_date,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editTeacherModal').modal('hide');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        let firstError = Object.values(errors)[0][0]; // first error message

                        alertify.alert()
                            .setting({
                                title: 'Validation Error',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                            }).show();
                    } else {
                        alertify.alert()
                            .setting({
                                title: 'Server Error',
                                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                            }).show();
                    }
                }
            });
        }   
    </script>
    <x-delete-confirm functionName="deleteTeacher" deleteUrl="teachers.destroy" />
@endpush