<!-- Add Timetable Modal -->
<div class="modal fade" id="addTimetableModal" tabindex="-1" aria-labelledby="addTimetableModalLabel">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addTimetableModalLabel">Add Timetable</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                    <select name="class_id" id="timetable_class_id" class="form-select" onclick="selectClass()">
                        
                    </select>
                </div>
                <div class="mb-3">  
                    <label for="section_id" class="form-label">Section
                        <button class="btn btn-sm btn-circle btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </label>
                    <select name="section_id" id="timetable_section_id" class="form-select">

                    </select>
                </div>
                <div class="mb-3">
                    <label for="day" class="form-label">Day <span class="text-danger">*</span></label>
                    <select name="day" id="timetable_day" class="form-select" required>
                        @foreach ($days as $day => $name)
                            <option value="{{ $day }}">{{ $name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3">
                    <label for="period_id" class="form-label">Period <span class="text-danger">*</span></label>
                    <select name="period_id" id="timetable_period_id" class="form-select" required>

                    </select>
                </div>
                <div class="mb-3">
                    <label for="subject_id" class="form-label">Subject <span class="text-danger">*</span>
                        <button class="btn btn-sm btn-circle btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </label>
                    <select name="subject_id" id="timetable_subject_id" class="form-select" required>

                    </select>
                </div>
                <div class="mb-3">
                    <label for="teacher_id" class="form-label">Teacher <span class="text-danger">*</span>
                        <button class="btn btn-sm btn-circle btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                            <i class="fa-solid fa-plus"></i>
                        </button>
                    </label>
                    <select name="teacher_id" id="timetable_teacher_id" class="form-select" required>

                    </select>
                </div>
                <div class="mb-3">
                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                    <select name="status" id="timetable_status" class="form-select" required>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-success" onclick="addTimetable()">Save Timetable</button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Store    
    function addTimetable(){
        const class_id = $('#timetable_class_id').val();
        const subject_id = $('#timetable_subject_id').val(); 
        const day = $('#timetable_day').val();    
        const period_id = $('#timetable_period_id').val();      
        const teacher_id = $('#timetable_teacher_id').val();    
        const section_id = $('#timetable_section_id').val();    
        const status = $('#timetable_status').val();

        if(class_id == '' || subject_id == '' || day == '' || teacher_id == '' || period_id == '' || status == ''){
            alertify.alert().setting({
                title: 'Error!',
                message: 'Please fill in all required fields: Class, Subject, Day, Period, Teacher, and Status. Section is optional.',
            }).show();
            return false;
        }

        $.ajax({
            type: "POST",
            url: "{{ route('timetables.store') }}",
            data: {
                'class_id': class_id,   
                'subject_id': subject_id,
                'day': day,
                'period_id': period_id,
                'teacher_id': teacher_id,
                'section_id': section_id,
                'status': status,
            },
            success: function (response) {
                if(response.status == true){
                    search();
                    $('#addTimetableModal').modal('hide');
                    $('#timetable_class_id').val('');
                    $('#timetable_subject_id').val('');
                    $('#timetable_day').val('monday');
                    $('#timetable_period_id').val('');
                    $('#timetable_teacher_id').val('');
                    $('#timetable_section_id').val('');
                    alertify.success(response.message);
                }else{
                    alertify.alert()
                        .setting({
                            title: 'Error!',
                            message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                        }).show();
                }
            },
            @include('admin.includes.ajax-error')
        });

    }
</script>