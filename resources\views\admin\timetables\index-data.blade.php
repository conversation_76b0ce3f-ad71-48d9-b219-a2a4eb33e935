@forelse($records as $row)
    <tr>
        <td>{{ $loop->iteration }}</td>
        <td>{{ $row->class->name ?? '' }}</td>
        <td>{{ $row->section->name ?? 'N/A' }} </td>
        <td>{{ $row->day ?? '' }}</td>
        <td>{{ $row->period->name ?? '' }}</td>
        <td>{{ $row->subject->name ?? '' }} ({{ $row->subject->code ?? '' }})</td>
        <td>{{ $row->teacher->user->name ?? '' }} ({{ $row->teacher->emp_code ?? '' }})</td>
        <td class="text-center">
            @include('admin.includes.status')
        </td>
        <td class="d-flex justify-content-center gap-2">
            <button class="btn btn-sm btn-outline-warning" title="Edit Timetable" onclick="editTimetable({{ $row->id }})">
                <i class="fa-solid fa-pen-to-square"></i>
                <input type="hidden" id="edit_timetables_class_id_data{{ $row->id }}" value="{{ $row->class->id ?? '' }}">
                <input type="hidden" id="edit_timetables_section_id_data{{ $row->id }}" value="{{ $row->section->id ?? '' }}">
                <input type="hidden" id="edit_timetables_day_data{{ $row->id }}" value="{{ $row->day ?? '' }}">
                <input type="hidden" id="edit_timetables_subject_id_data{{ $row->id }}" value="{{ $row->subject->id ?? '' }}">
                <input type="hidden" id="edit_timetables_teacher_id_data{{ $row->id }}" value="{{ $row->teacher->id ?? '' }}">
                <input type="hidden" id="edit_timetables_period_id_data{{ $row->id }}" value="{{ $row->period->id ?? '' }}">
                <input type="hidden" id="edit_timetables_status_data{{ $row->id }}" value="{{ $row->status }}">
            </button>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Timetable" onclick="deleteTimetable({{ $row->id }})">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="9" class="text-center">No Timetables found.</td>
    </tr>
@endforelse