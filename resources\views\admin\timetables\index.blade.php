@extends('layouts.admin') {{-- Your main layout file --}}
@section('title', 'Timetables')

@section('content')
    <x-alert />
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>Timetables Management</h4>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTimetableModal">
                <i class="fas fa-plus"></i> Add Timetable
            </button>
        </div>

        <!-- Modern Filter UI -->
        <div class="row align-items-center g-2 mb-4">
            <div class="col-md-3">
                <input type="text" class="form-control" id="ts_class_name_search" placeholder="Search by Class Name...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ts_section_name_search" placeholder="Search by Section Name...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ts_subject_name_search" placeholder="Search by Subject Name...">
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ts_teacher_name_search" placeholder="Search by Teacher Name...">
            </div>
            <div class="col-md-3">
                <select name="day" id="ts_day_search" class="form-select">
                    <option value="">Select Day</option>
                    @foreach ($days as $day => $name)
                        <option value="{{ $day }}">{{ $name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ts_period_name_search" placeholder="Search by Period Name...">
            </div>
            <div class="col-md-3">
                <select name="status" id="ts_status_search" class="form-select">
                    <option value="">Select Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div class="col-auto">
                <button class="btn btn-success px-4" onclick="search()">
                    <i class="fa fa-search me-1"></i> Search
                </button>
            </div>
            <div class="col-auto">
                <button class="btn btn-secondary px-4" onclick="resetSearchForm()">
                    <i class="fa fa-undo me-1"></i> Reset
                </button>
            </div>
        </div>

        <!-- Timetables Table -->
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>#</th>
                        <th>Class</th>
                        <th>Section</th>
                        <th>Day</th>
                        <th>Period</th>
                        <th>Subject</th>
                        <th>Teacher</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    @include('admin.timetables.index-data')
                </tbody>
            </table>
            <div class="mt-2" id="pagination_links">
                {{ $records->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>

    <!-- Add  Modal -->
    @include('admin.timetables.add-modal')

    <!-- Edit  Modal -->
    <div class="modal fade" id="editTimetableModal" tabindex="-1" aria-labelledby="editTeacherSubjectModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editTeacherSubjectModalLabel">Update Timetable</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="edit_timetable_id">
                    <div class="mb-3">
                        <label for="class_id" class="form-label">Class <span class="text-danger">*</span></label>
                        <select name="class_id" id="edit_timetable_class_id" class="form-select" onclick="selectClassForEdit()">

                        </select>
                    </div>
                    <div class="mb-3">  
                        <label for="section_id" class="form-label">Section</label>
                        <select name="section_id" id="edit_timetable_section_id" class="form-select">

                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="day" class="form-label">Day <span class="text-danger">*</span></label>
                        <select name="day" id="edit_timetable_day" class="form-select" required>
                            @foreach ($days as $day => $name)
                                <option value="{{ $day }}">{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="period_id" class="form-label">Period <span class="text-danger">*</span></label>
                        <select name="period_id" id="edit_timetable_period_id" class="form-select" required>

                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="subject_id" class="form-label">Subject <span class="text-danger">*</span></label>
                        <select name="subject_id" id="edit_timetable_subject_id" class="form-select" required>

                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="teacher_id" class="form-label">Teacher <span class="text-danger">*</span></label>
                        <select name="teacher_id" id="edit_timetable_teacher_id" class="form-select" required>

                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select name="status" id="edit_timetable_status" class="form-select" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success" onclick="updateTimetable()">Update Timetable</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Get classes
        function getClasses(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.classes') }}",
                success: function (response) {
                    $('#timetable_class_id').html(response.html);
                    $('#edit_timetable_class_id').html(response.html);
                }
            });
        }

        // Get subjects
        function getSubjects(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.subjects') }}",
                success: function (response) {
                    $('#timetable_subject_id').html(response.html);
                    $('#edit_timetable_subject_id').html(response.html);
                }
            });
        }

        // Get teachers
        function getTeachers(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.teachers') }}",
                success: function (response) {
                    $('#timetable_teacher_id').html(response.html);
                    $('#edit_timetable_teacher_id').html(response.html);
                }
            });
        }

        // Get periods
        function getPeriods(){
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.periods') }}",
                success: function (response) {
                    $('#timetable_period_id').html(response.html);
                    $('#edit_timetable_period_id').html(response.html);
                }
            });
        }   

        getClasses();
        getSubjects();
        getTeachers();
        getPeriods();
    </script>
    <script>
        // Get sections
        function selectClass(){
            const class_id = $('#timetable_class_id').val();
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#timetable_section_id').html(response.html);
                }
            });
        }

        // Get sections for edit
        function selectClassForEdit(sectionId = null){
            const class_id = $('#edit_timetable_class_id').val();
            $.ajax({
                type: "GET",
                url: "{{ route('admin.dropdown.sections') }}",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#edit_timetable_section_id').html(response.html);
                    if(sectionId != null){
                        $('#edit_timetable_section_id').val(sectionId);
                    }
                }
            });
        }
    </script>
    <script>
        function search(){
            const class_name = $('#ts_class_name_search').val();
            const subject_name = $('#ts_subject_name_search').val();
            const teacher_name = $('#ts_teacher_name_search').val();
            const section_name = $('#ts_section_name_search').val();
            const day = $('#ts_day_search').val();
            const period_name = $('#ts_period_name_search').val();
            const status = $('#ts_status_search').val();

            $.ajax({
                type: "GET",
                url: "{{ route('timetables.index') }}",
                data: {
                    'class_name': class_name,
                    'subject_name': subject_name,
                    'teacher_name': teacher_name,
                    'section_name': section_name,
                    'day': day,
                    'period_name': period_name,
                    'status': status
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#ts_class_name_search').val('');
            $('#ts_subject_name_search').val('');
            $('#ts_teacher_name_search').val('');
            $('#ts_section_name_search').val('');
            $('#ts_day_search').val('');
            $('#ts_period_name_search').val('');
            $('#ts_status_search').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            const url = $(this).attr('href');
            const class_name = $('#ts_class_name_search').val();
            const subject_name = $('#ts_subject_name_search').val();
            const teacher_name = $('#ts_teacher_name_search').val();
            const section_name = $('#ts_section_name_search').val();
            const day = $('#ts_day_search').val();
            const period_name = $('#ts_period_name_search').val();
            const status = $('#ts_status_search').val();
            
            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'class_name': class_name,
                    'subject_name': subject_name,
                    'teacher_name': teacher_name,
                    'section_name': section_name,
                    'day': day,
                    'period_name': period_name,
                    'status': status
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        // Edit 
        function editTimetable(id){
            $('#edit_timetable_id').val(id);
            $('#edit_timetable_class_id').val($('#edit_timetables_class_id_data'+id).val());
            $('#edit_timetable_subject_id').val($('#edit_timetables_subject_id_data'+id).val());
            $('#edit_timetable_teacher_id').val($('#edit_timetables_teacher_id_data'+id).val());
            $('#edit_timetable_section_id').val($('#edit_timetables_section_id_data'+id).val());
            $('#edit_timetable_day').val($('#edit_timetables_day_data'+id).val());
            $('#edit_timetable_period_id').val($('#edit_timetables_period_id_data'+id).val());
            const sectionId = $('#edit_timetables_section_id_data'+id).val();
            selectClassForEdit(sectionId);
            $('#editTimetableModal').modal('show');
        }

        // Update
        function updateTimetable(){
            const id = $('#edit_timetable_id').val();
            const class_id = $('#edit_timetable_class_id').val();
            const subject_id = $('#edit_timetable_subject_id').val();    
            const teacher_id = $('#edit_timetable_teacher_id').val();    
            const section_id = $('#edit_timetable_section_id').val();    
            const day = $('#edit_timetable_day').val();
            const period_id = $('#edit_timetable_period_id').val();
            const status = $('#edit_timetable_status').val();

            if(class_id == '' || subject_id == '' || day == '' || teacher_id == '' || period_id == '' || status == ''){
                alertify.alert().setting({
                    title: 'Error!',
                    message: 'Please fill in all required fields: Class, Subject, Day, Period, Teacher, and Status. Section is optional.',
                }).show();
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{{ route('timetables.update', ':id') }}".replace(':id', id),
                data: {
                    'class_id': class_id,
                    'subject_id': subject_id,
                    'day': day,
                    'teacher_id': teacher_id,
                    'section_id': section_id,
                    'period_id': period_id,
                    'status': status,
                    '_method': 'PUT',
                },
                success: function (response) {
                    if(response.status == true){
                        search();
                        $('#editTimetableModal').modal('hide');
                        $('#edit_timetable_class_id').val('');
                        $('#edit_timetable_subject_id').val('');
                        $('#edit_timetable_day').val('monday');
                        $('#edit_timetable_period_id').val('');
                        $('#edit_timetable_teacher_id').val('');
                        $('#edit_timetable_section_id').val('');
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
                error: function (xhr) {
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        let firstError = Object.values(errors)[0][0]; // first error message

                        alertify.alert()
                            .setting({
                                title: 'Validation Error',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + firstError
                            }).show();
                    }
                    else if (xhr.status === 400) {
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + xhr.responseJSON.message
                            }).show();
                    } else {
                        alertify.alert()
                            .setting({
                                title: 'Server Error',
                                message: '<i class="fa-solid fa-triangle-exclamation text-danger me-2"></i> Something went wrong.'
                            }).show();
                    }
                }
            });
        }
    </script>

    <!-- Add Modal -->
    @include('admin.sections.add-modal')
    @include('admin.subjects.add-modal')
    @include('admin.teachers.add-modal')

    <x-delete-confirm functionName="deleteTimetable" deleteUrl="timetables.destroy" />
@endpush
