<script>
    function {{ $functionName }}(id) {
        let deleteUrl = "{{ route($deleteUrl, ':id') }}".replace(':id', id);
        alertify.confirm(
            'Confirm Delete',
            'Are you sure you want to delete?',
            function () {

                // ✅ User clicked "Yes"
                $.ajax({
                    type: "POST",
                    url: deleteUrl, // or use: `{{ url('sections') }}/${id}`
                    data: {
                        '_method': 'DELETE',
                    },
                    success: function (response) {
                        if(response.code == 200){
                            alertify.success(response.message);
                            search();
                        }else{
                            alertify.error(response.message);
                        }
                    }
                });
                
            },
            function () {
                // ❌ User clicked "No"
                alertify.error('Delete cancelled');
            }
        ).set('labels', {ok: 'Yes', cancel: 'No'});
    }
</script>