<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SchoolClassController;
use App\Http\Controllers\SectionController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\ClassSubjectController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\TeacherController;

Route::get('/', function () {
    return to_route('login');
});

Route::prefix('panel')->group(function () {
    Route::get('login',[AuthController::class,'login'])->name('login');
    Route::post('login',[AuthController::class,'loginSubmit'])->name('login.submit');

    Route::middleware(['auth', 'check_role_permission'])->group(function () {
        Route::controller(AuthController::class)->group(function(){
            Route::get('dashboard','dashboard')->name('dashboard');
            Route::get('logout','logout')->name('logout');
        });

        Route::resource('roles',RoleController::class);

        // Academic
        Route::resource('classes',SchoolClassController::class);
        Route::resource('sections',SectionController::class);
        Route::resource('subjects',SubjectController::class);
        Route::resource('class-subjects',ClassSubjectController::class);

        // superadmin, admin, teacher, student, parent
        Route::resource('users', UserController::class);

        //Teacher
        Route::resource('teachers', TeacherController::class);

        // Test Multiple Role Permission
        Route::get('superadmin-profile', function(){
            return "superadmin-profile";
        })->name('superadmin-profile');
        Route::get('admin-profile', function(){
            return "admin-profile";
        })->name('admin-profile');
        Route::get('teacher-profile', function(){
            return "teacher-profile";
        })->name('teacher-profile');
        Route::get('student-profile', function(){
            return "student-profile";
        })->name('student-profile');
        Route::get('parent-profile', function(){
            return "parent-profile";
        })->name('parent-profile');
    });
});