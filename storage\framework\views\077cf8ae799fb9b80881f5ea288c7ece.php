<?php $__env->startSection('title', 'Student Attendances'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    body {
        background-color: #f8f9fa;
    }

    .attendance-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 20px 0;
    }

    .attendance-header {
        background: white;
        padding: 20px 24px;
        border-bottom: 1px solid #e9ecef;
    }

    .attendance-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .attendance-subtitle {
        font-size: 14px;
        color: #6c757d;
        margin: 4px 0 0 0;
    }

    .search-filters {
        padding: 20px 24px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .search-input {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 10px 16px;
        font-size: 14px;
        background: white;
        transition: all 0.3s ease;
        width: 100%;
    }

    .search-input:focus {
        border-color: #4285f4;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        outline: none;
    }

    .search-input::placeholder {
        color: #9ca3af;
    }

    .btn-search {
        background: #4285f4;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-search:hover {
        background: #3367d6;
        color: white;
    }

    .btn-report {
        background: #34a853;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 10px;
    }

    .btn-report:hover {
        background: #2d8f47;
        color: white;
    }

    .attendance-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .attendance-table thead th {
        background: #f8f9fa;
        color: #5f6368;
        font-weight: 500;
        font-size: 14px;
        padding: 16px 20px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }

    .attendance-table tbody td {
        padding: 16px 20px;
        border-bottom: 1px solid #f1f3f4;
        font-size: 14px;
        color: #3c4043;
        vertical-align: middle;
    }

    .attendance-table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .member-info {
        display: flex;
        align-items: center;
    }

    .member-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #e8f0fe;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 14px;
        color: #4285f4;
        font-weight: 500;
    }

    .member-name {
        font-weight: 500;
        color: #3c4043;
    }

    .member-phone {
        color: #5f6368;
        font-size: 13px;
    }

    .check-in-btn {
        background: #4285f4;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .check-in-btn:hover {
        background: #3367d6;
    }

    .check-in-btn:disabled {
        background: #e8eaed;
        color: #9aa0a6;
        cursor: not-allowed;
    }

    .status-present {
        color: #34a853;
        font-weight: 500;
    }

    .status-absent {
        color: #ea4335;
        font-weight: 500;
    }

    .status-not-marked {
        color: #fbbc04;
        font-weight: 500;
    }

    .attendance-actions {
        display: flex;
        gap: 8px;
    }

    .btn-present {
        background: #34a853;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-present:hover {
        background: #2d8f47;
    }

    .btn-absent {
        background: #ea4335;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-absent:hover {
        background: #d33b2c;
    }

    .btn-present:disabled,
    .btn-absent:disabled {
        background: #e8eaed;
        color: #9aa0a6;
        cursor: not-allowed;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #5f6368;
    }

    .empty-state i {
        font-size: 48px;
        color: #dadce0;
        margin-bottom: 16px;
    }

    .empty-state h5 {
        color: #3c4043;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .pagination-container {
        padding: 20px 24px;
        border-top: 1px solid #e9ecef;
        background: white;
    }

    @media (max-width: 768px) {
        .attendance-container {
            margin: 10px;
            border-radius: 8px;
        }

        .attendance-header,
        .search-filters {
            padding: 16px;
        }

        .attendance-table thead th,
        .attendance-table tbody td {
            padding: 12px 16px;
        }

        .member-avatar {
            width: 28px;
            height: 28px;
            font-size: 12px;
        }

        .attendance-actions {
            flex-direction: column;
            gap: 4px;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <?php if (isset($component)) { $__componentOriginalb5e767ad160784309dfcad41e788743b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb5e767ad160784309dfcad41e788743b = $attributes; } ?>
<?php $component = App\View\Components\Alert::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Alert::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb5e767ad160784309dfcad41e788743b)): ?>
<?php $attributes = $__attributesOriginalb5e767ad160784309dfcad41e788743b; ?>
<?php unset($__attributesOriginalb5e767ad160784309dfcad41e788743b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb5e767ad160784309dfcad41e788743b)): ?>
<?php $component = $__componentOriginalb5e767ad160784309dfcad41e788743b; ?>
<?php unset($__componentOriginalb5e767ad160784309dfcad41e788743b); ?>
<?php endif; ?>

    <!-- Attendance Container -->
    <div class="attendance-container">
        <!-- Header -->
        <div class="attendance-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1 class="attendance-title">
                        <i class="fas fa-users me-2"></i>Student Attendance
                    </h1>
                    <p class="attendance-subtitle">Manage student attendance records</p>
                </div>
                <button type="button" class="btn-report">
                    <i class="fas fa-chart-bar me-2"></i>Attendance Report
                </button>
            </div>
        </div>

        <!-- Search Filters -->
        <div class="search-filters">
            <div class="row g-3 align-items-end">
                <div class="col-md-3">
                    <input type="text"
                           class="search-input"
                           id="student_name_search"
                           placeholder="student first name...">
                </div>
                <div class="col-md-3">
                    <select name="class_id" id="class_id" class="search-input" onchange="sections()">
                        <option value="">Select Class</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="section_id" id="section_id" class="search-input">
                        <option value="">Select Section</option>
                    </select>
                </div>
                <div class="col-auto">
                    <button type="button" class="btn-search" onclick="search()">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th>SN.</th>
                        <th>Student Name</th>
                        <th>Student Phone</th>
                        <th>Date</th>
                        <th>Check In</th>
                        <th>Check Out</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="list_data">
                    <?php echo $__env->make('admin.student-attendances.index-data', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" id="pagination_links">
            <?php echo e($records->links('pagination::bootstrap-5')); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function search(){
            const student_name = $('#student_name_search').val();
            const class_id = $('#class_id').val();
            const section_id = $('#section_id').val();

            $.ajax({
                type: "GET",
                url: "<?php echo e(route('student-attendances.index')); ?>",
                data: {
                    'student_name': student_name,
                    'class_id': class_id,
                    'section_id': section_id
                },
                success: function (response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        }

        // Reset search form
        function resetSearchForm(){
            $('#student_name_search').val('');
            $('#class_id').val('');
            $('#section_id').val('');
            search();
        }

        // Handle pagination link clicks
        $(document).on('click', '#pagination_links a', function(e){
            e.preventDefault();
            let url = $(this).attr('href');
            const student_name = $('#student_name_search').val();
            const class_id = $('#class_id').val();
            const section_id = $('#section_id').val();

            $.ajax({
                url: url,
                type: "GET",
                data: {
                    'student_name': student_name,
                    'class_id': class_id,
                    'section_id': section_id
                },
                success: function(response) {
                    $('#list_data').html(response.html);
                    $('#pagination_links').html(response.pagination);
                }
            });
        });
    </script>

    <script>
        function classes(){
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('admin.dropdown.classes')); ?>",
                success: function (response) {
                    $('#class_id').html(response.html);
                }
            });
        }

        function sections(){
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('admin.dropdown.sections')); ?>",
                data: {
                    'class_id': $('#class_id').val(),
                },
                success: function (response) {
                    $('#section_id').html(response.html);
                }
            });
        }

        classes();
        sections();
    </script>   
    <script>
        function presentAttendance(id){
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('student-attendances.present')); ?>",
                data: {
                    'id': id,
                },
                success: function (response) {
                    if(response.status == true){
                        // Update check-in column
                        $('#check_in_' + id).html(response.check_in_time || 'Present');

                        // Update status column
                        $('#status_' + id).html('<span class="status-present">Present</span>');

                        // Show success message
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }

        function absentAttendance(id){
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('student-attendances.absent')); ?>",
                data: {
                    'id': id,
                },
                success: function (response) {
                    if(response.status == true){
                        // Update check-in column
                        $('#check_in_' + id).html('-');

                        // Update status column
                        $('#status_' + id).html('<span class="status-absent">Absent</span>');

                        // Show success message
                        alertify.success(response.message);
                    }else{
                        alertify.alert()
                            .setting({
                                title: 'Error!',
                                message: '<i class="fa-solid fa-circle-xmark text-danger me-2"></i>' + response.message
                            }).show();
                    }
                },
            });
        }
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/admin/student-attendances/index.blade.php ENDPATH**/ ?>