<?php $__empty_1 = true; $__currentLoopData = $records; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td><?php echo e($row->student->user->name ?? ''); ?></td>
        <td><?php echo e($row->roll_no ?? ''); ?></td>
        <td><?php echo e(date('d-m-Y')); ?></td>
        <td>
            <button class="btn btn-sm btn-primary" title="Present Attendance" onclick="presentAttendance(<?php echo e($row->student->id); ?>)"
                <?php echo e(isset($row->student->studentAttendance->status) ? 'disabled' : ''); ?>

                id="present_btn_<?php echo e($row->student->id); ?>"
            >
                Present
            </button>
            <button class="btn btn-sm btn-danger" title="Absent Attendance" onclick="absentAttendance(<?php echo e($row->student->id); ?>)"
                <?php echo e(isset($row->student->studentAttendance->status) ? 'disabled' : ''); ?>

                id="absent_btn_<?php echo e($row->student->id); ?>"
            >
                Absent
            </button>
        </td>
        <td id="marked_by<?php echo e($row->student->id); ?>"><?php echo e($row->student->studentAttendance->markedBy->name ?? ''); ?></td>
        <td id="check_in_time<?php echo e($row->student->id); ?>"><?php echo e($row->student->studentAttendance->check_in_time ?? ''); ?></td>
        <td class="text-center" id="status<?php echo e($row->student->id); ?>">
            
                <span class="badge bg-success" id="present_status<?php echo e($row->student->id); ?>" style="<?php echo e(isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'present' ? '' : 'display:none'); ?>"><?php echo e(ucfirst($row->student->studentAttendance->status ?? '')); ?></span>
            
                <span class="badge bg-danger" id="absent_status<?php echo e($row->student->id); ?>" style="<?php echo e(isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'absent' ? '' : 'display:none'); ?>"><?php echo e(ucfirst($row->student->studentAttendance->status ?? '')); ?></span>
            
                <span class="badge bg-warning" id="not_marked_status<?php echo e($row->student->id); ?>" style="<?php echo e(isset($row->student->studentAttendance->status) ? 'display:none' : ''); ?>">Not Marked</span>
            
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <tr>
        <td colspan="8" class="text-center">No Student Attendances found.</td>
    </tr>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/admin/student-attendances/index-data.blade.php ENDPATH**/ ?>