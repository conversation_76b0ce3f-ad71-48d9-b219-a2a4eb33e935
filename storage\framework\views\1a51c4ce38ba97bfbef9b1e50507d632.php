<?php $__empty_1 = true; $__currentLoopData = $records; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td>
            <div class="member-info">
                <div class="member-avatar">
                    <?php echo e(strtoupper(substr($row->student->user->name ?? 'S', 0, 1))); ?>

                </div>
                <div class="member-name"><?php echo e($row->student->user->name ?? ''); ?></div>
            </div>
        </td>
        <td class="member-phone"><?php echo e($row->parent_contact_no ?? 'N/A'); ?></td>
        <td><?php echo e(date('d-m-Y')); ?></td>
        <td id="check_in_<?php echo e($row->student->id); ?>">
            <?php if(isset($row->student->studentAttendance->status)): ?>
                <?php if($row->student->studentAttendance->check_in_time): ?>
                    <?php echo e($row->student->studentAttendance->check_in_time); ?>

                <?php else: ?>
                    -
                <?php endif; ?>
            <?php else: ?>
                <button class="check-in-btn"
                        onclick="presentAttendance(<?php echo e($row->student->id); ?>)"
                        id="present_btn_<?php echo e($row->student->id); ?>">
                    Check-In
                </button>
            <?php endif; ?>
        </td>
        <td id="check_out_<?php echo e($row->student->id); ?>">
            <?php if(isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'present'): ?>
                Not checked out yet!
            <?php else: ?>
                -
            <?php endif; ?>
        </td>
        <td id="status_<?php echo e($row->student->id); ?>">
            <?php if(isset($row->student->studentAttendance->status)): ?>
                <?php if($row->student->studentAttendance->status == 'present'): ?>
                    <span class="status-present">Present</span>
                <?php else: ?>
                    <span class="status-absent">Absent</span>
                <?php endif; ?>
            <?php else: ?>
                <div class="attendance-actions">
                    <button class="btn-present"
                            onclick="presentAttendance(<?php echo e($row->student->id); ?>)"
                            id="present_btn_alt_<?php echo e($row->student->id); ?>">
                        Present
                    </button>
                    <button class="btn-absent"
                            onclick="absentAttendance(<?php echo e($row->student->id); ?>)"
                            id="absent_btn_<?php echo e($row->student->id); ?>">
                        Absent
                    </button>
                </div>
            <?php endif; ?>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <tr>
        <td colspan="7">
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <h5>No Student Attendances Found</h5>
                <p>Please select a class and section to view attendance records.</p>
            </div>
        </td>
    </tr>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/admin/student-attendances/index-data.blade.php ENDPATH**/ ?>