<?php $__empty_1 = true; $__currentLoopData = $records; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <tr>
        <td>
            <div class="fw-bold text-primary"><?php echo e($loop->iteration); ?></div>
        </td>
        <td>
            <div class="d-flex align-items-center">
                <div class="avatar-sm me-3">
                    <img src="<?php echo e($row->student->user->photo ? asset('storage/'.$row->student->user->photo) : asset('assets/images/default-avatar.png')); ?>"
                         alt="Student"
                         class="rounded-circle"
                         width="40"
                         height="40"
                         style="object-fit: cover;">
                </div>
                <div>
                    <div class="fw-semibold text-dark"><?php echo e($row->student->user->name ?? ''); ?></div>
                    <small class="text-muted"><?php echo e($row->student->user->email ?? ''); ?></small>
                </div>
            </div>
        </td>
        <td>
            <span class="badge bg-info"><?php echo e($row->roll_no ?? 'N/A'); ?></span>
        </td>
        <td>
            <div class="fw-semibold"><?php echo e(date('d-m-Y')); ?></div>
            <small class="text-muted"><?php echo e(date('l')); ?></small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-attendance btn-present"
                        title="Mark Present"
                        onclick="presentAttendance(<?php echo e($row->student->id); ?>)"
                        <?php echo e(isset($row->student->studentAttendance->status) ? 'disabled' : ''); ?>

                        id="present_btn_<?php echo e($row->student->id); ?>">
                    <i class="fas fa-check me-1"></i>Present
                </button>
                <button class="btn btn-attendance btn-absent"
                        title="Mark Absent"
                        onclick="absentAttendance(<?php echo e($row->student->id); ?>)"
                        <?php echo e(isset($row->student->studentAttendance->status) ? 'disabled' : ''); ?>

                        id="absent_btn_<?php echo e($row->student->id); ?>">
                    <i class="fas fa-times me-1"></i>Absent
                </button>
            </div>
        </td>
        <td id="marked_by<?php echo e($row->student->id); ?>">
            <?php if($row->student->studentAttendance->markedBy ?? null): ?>
                <div class="fw-semibold"><?php echo e($row->student->studentAttendance->markedBy->name); ?></div>
                <small class="text-muted"><?php echo e($row->student->studentAttendance->created_at->format('H:i A')); ?></small>
            <?php else: ?>
                <span class="text-muted">-</span>
            <?php endif; ?>
        </td>
        <td id="check_in_time<?php echo e($row->student->id); ?>">
            <?php if($row->student->studentAttendance->check_in_time ?? null): ?>
                <div class="fw-semibold text-success">
                    <i class="fas fa-clock me-1"></i><?php echo e($row->student->studentAttendance->check_in_time); ?>

                </div>
            <?php else: ?>
                <span class="text-muted">-</span>
            <?php endif; ?>
        </td>
        <td class="text-center" id="status<?php echo e($row->student->id); ?>">
            <span class="status-badge status-present"
                  id="present_status<?php echo e($row->student->id); ?>"
                  style="<?php echo e(isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'present' ? '' : 'display:none'); ?>">
                <i class="fas fa-check me-1"></i>Present
            </span>

            <span class="status-badge status-absent"
                  id="absent_status<?php echo e($row->student->id); ?>"
                  style="<?php echo e(isset($row->student->studentAttendance->status) && $row->student->studentAttendance->status == 'absent' ? '' : 'display:none'); ?>">
                <i class="fas fa-times me-1"></i>Absent
            </span>

            <span class="status-badge status-not-marked"
                  id="not_marked_status<?php echo e($row->student->id); ?>"
                  style="<?php echo e(isset($row->student->studentAttendance->status) ? 'display:none' : ''); ?>">
                <i class="fas fa-question me-1"></i>Not Marked
            </span>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <tr>
        <td colspan="8" class="text-center py-5">
            <div class="text-muted">
                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                <h5>No Student Attendances Found</h5>
                <p>Please select a class and section to view attendance records.</p>
            </div>
        </td>
    </tr>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/admin/student-attendances/index-data.blade.php ENDPATH**/ ?>