<?php $__empty_1 = true; $__currentLoopData = $records; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <tr>
        <td><?php echo e($loop->iteration); ?></td>
        <td><?php echo e($row->user->name ?? ''); ?></td>
        <td><?php echo e($row->user->email ?? ''); ?></td>
        <td><?php echo e($row->parent_contact_no ?? ''); ?></td>
        <td><?php echo e($row->user->address ?? ''); ?></td>
        <td class="text-center">
            <?php if($row->user->status == 'active'): ?>
                <span class="badge bg-success"><?php echo e(ucfirst($row->user->status)); ?></span>
            <?php else: ?>
                <span class="badge bg-danger"><?php echo e(ucfirst($row->user->status ?? '')); ?></span>
            <?php endif; ?>
        </td>
        <td class="d-flex justify-content-center gap-2">
            <a href="<?php echo e(route('students.show',$row->id)); ?>" class="btn btn-sm btn-outline-info" title="View Student Profile">
                <i class="fa-solid fa-eye"></i>
            </a>
            <a href="<?php echo e(route('students.edit',$row->id)); ?>" class="btn btn-sm btn-outline-warning" title="Edit Student Profile">
                <i class="fa-solid fa-pen-to-square"></i>
            </a>
            <button class="btn btn-sm btn-outline-danger" type="button" title="Delete Section" onclick="deleteStudent(<?php echo e($row->id); ?>)">
                <i class="fa-solid fa-trash"></i>
            </button>
        </td>
    </tr>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <tr>
        <td colspan="7" class="text-center">No Students found.</td>
    </tr>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/admin/students/index-data.blade.php ENDPATH**/ ?>