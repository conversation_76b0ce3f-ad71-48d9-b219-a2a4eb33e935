 
<?php $__env->startSection('title', 'Add Student'); ?>

<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginalb5e767ad160784309dfcad41e788743b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb5e767ad160784309dfcad41e788743b = $attributes; } ?>
<?php $component = App\View\Components\Alert::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('alert'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Alert::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb5e767ad160784309dfcad41e788743b)): ?>
<?php $attributes = $__attributesOriginalb5e767ad160784309dfcad41e788743b; ?>
<?php unset($__attributesOriginalb5e767ad160784309dfcad41e788743b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb5e767ad160784309dfcad41e788743b)): ?>
<?php $component = $__componentOriginalb5e767ad160784309dfcad41e788743b; ?>
<?php unset($__componentOriginalb5e767ad160784309dfcad41e788743b); ?>
<?php endif; ?>
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4>
                <?php if(isset($student)): ?>
                    Update Student
                <?php else: ?>
                    Add Student
                <?php endif; ?>
            </h4>
            <a class="btn btn-primary" href="<?php echo e(route('students.index')); ?>">
                <i class="fas fa-list"></i> Students List
            </a>
        </div>

        <!-- Modern Filter UI --> 
        <div class="card shadow">
            <div class="card-body" style="background-color:rgb(191, 194, 194)">
                <?php if(isset($student)): ?>
                    <form action="<?php echo e(route('students.update',$student->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                <?php else: ?>
                <form action="<?php echo e(route('students.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                <?php endif; ?>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="<?php echo e(old('name',$student->user->name ?? '')); ?>" maxlength="70" required>  
                        </div>
                        <div class="col-md-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" name="email" value="<?php echo e(old('email',$student->user->email ?? '')); ?>" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="password" class="form-label">Password 
                                <?php if(empty($student->user->password)): ?>
                                    <span class="text-danger">*</span>
                                <?php endif; ?>
                            </label>
                            <input type="password" name="password" class="form-control" value="<?php echo e(old('password')); ?>" id="password" maxlength="20"
                                <?php if(empty($student->user->password)): ?> required <?php endif; ?> >  
                        </div>
                        <div class="col-md-3">
                            <label for="father_name" class="form-label">Father Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="father_name" value="<?php echo e(old('father_name',$student->father_name ?? '')); ?>" maxlength="70" required>
                        </div>
                        <div class="col-md-3">
                            <label for="mother_name" class="form-label">Mother Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="mother_name" value="<?php echo e(old('mother_name',$student->mother_name ?? '')); ?>" maxlength="70" required>
                        </div>
                        <div class="col-md-3">
                            <label for="parent_contact_no" class="form-label">Parent Contact No. <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" value="<?php echo e(old('parent_contact_no',$student->parent_contact_no ?? '')); ?>" name="parent_contact_no" required>
                        </div>
                        <div class="col-md-3">
                            <label for="father_occupation" class="form-label">Father Occupation</label>
                            <input type="text" class="form-control" value="<?php echo e(old('father_occupation',$student->father_occupation ?? '')); ?>" name="father_occupation" maxlength="70">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="dob" class="form-label">DOB <span class="text-danger">*</span></label>
                            <input type="text" class="form-control datepicker" value="<?php echo e(old('dob',$student->dob ?? '')); ?>" id="dob" name="dob" autocomplete="OFF" required>  
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="gender" class="form-label">Gender <span class="text-danger"></span></label>
                            <select name="gender" id="gender" class="form-select" required>
                                <option value="">Select Gender</option>
                                <option value="Male" <?php echo e(old('gender',$student->gender ?? '') == 'Male' ? 'selected' : ''); ?>>Male</option>
                                <option value="Female" <?php echo e(old('gender',$student->gender ?? '') == 'Female' ? 'selected' : ''); ?>>Female</option>
                                <option value="Transgender" <?php echo e(old('gender',$student->gender ?? '') == 'Transgender' ? 'selected' : ''); ?>>Transgender</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="blood_group" class="form-label">Blood Group</label>
                            <select name="blood_group" id="blood_group" class="form-select">
                                <option value="">Select Blood Group</option>
                                <?php $__currentLoopData = $bloodGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>" <?php echo e(old('blood_group',$student->blood_group ?? '') == $key ? 'selected' : ''); ?>><?php echo e($value); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="caste" class="form-label"> Caste <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="caste" value="<?php echo e(old('caste',$student->caste ?? '')); ?>" maxlength="20" required>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-select" required>
                                <option value="active" <?php echo e(old('status',$student->user->status ?? '') == 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="inactive" <?php echo e(old('status',$student->user->status ?? '') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="address" class="form-label"> Address <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="address" value="<?php echo e(old('address',$student->user->address ?? '')); ?>" maxlength="255" required>
                        </div>


                        

                        
                        <div class="col-auto">
                            <button class="btn btn-success px-4" type="submit">
                                <i class="fa fa-save me-1"></i>Submit
                            </button>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-secondary px-4" type="reset">
                                <i class="fa fa-undo me-1"></i> Reset
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Get classes
        function getClasses(){
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('admin.dropdown.classes')); ?>",
                success: function (response) {
                    $('#student_class_id').html(response.html);
                    $('#edit_student_class_id').html(response.html);
                }
            });
        }
        getClasses();

        // Get sections
        function selectClass(){
            const class_id = $('#student_class_id').val();
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('admin.dropdown.sections')); ?>",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#student_section_id').html(response.html);
                }
            });
        }

        // Get sections for edit
        function selectClassForEdit(sectionId = null){
            const class_id = $('#edit_student_class_id').val();
            $.ajax({
                type: "GET",
                url: "<?php echo e(route('admin.dropdown.sections')); ?>",
                data: {
                    'class_id': class_id,
                },
                success: function (response) {
                    $('#edit_student_section_id').html(response.html);
                    if(sectionId != null){
                        $('#edit_student_section_id').val(sectionId);
                    }
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/admin/students/create.blade.php ENDPATH**/ ?>