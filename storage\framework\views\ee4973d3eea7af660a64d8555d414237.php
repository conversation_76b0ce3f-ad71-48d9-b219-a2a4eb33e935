<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title><?php echo e(config('constant.app_name')); ?> - <?php echo $__env->yieldContent('title'); ?> </title>
    <link rel="icon" type="image/jpg" href="<?php echo e(asset('logo_favicon.jpg')); ?>">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('assets/bootstrap/bootstrap.min.css')); ?>">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/select/select2.min.css')); ?>">

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/admin/css/style.css')); ?>">

    
    <link rel="stylesheet" href="<?php echo e(asset('assets/jquery-ui/jquery-ui.min.css')); ?>">

    <?php echo $__env->yieldPushContent('styles'); ?>
    
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/alertify/alertify.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/alertify/alertify-bootstrap-theme.min.css')); ?>">

    
    <script src="<?php echo e(asset('assets/jquery/jquery.min.js')); ?>"></script>

    
    <script src="<?php echo e(asset('assets/alertify/alertify.min.js')); ?>"></script>
</head>
<body class="d-flex flex-column min-vh-100"> 

    <!-- Top Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <!-- Sidebar Toggle (Mobile and Tablet) -->
            <button class="btn btn-outline-light d-xl-none me-2" id="toggleSidebar" type="button">
                <i class="fas fa-bars"></i>
            </button>

            <!-- Brand -->
            <a class="navbar-brand flex-grow-1 flex-xl-grow-0" href="#">
                <span class="d-none d-sm-inline"><?php echo e(config('constant.app_name')); ?> Panel</span>
                <span class="d-sm-none"><?php echo e(config('constant.app_name')); ?></span>
            </a>

            <!-- Right Side Menu -->
            <div class="ms-auto">
                <ul class="navbar-nav align-items-center">
                    <!-- Admin Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1 me-sm-2"></i>
                            <span class="d-none d-sm-inline">Admin</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i> Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i> Settings</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-key me-2"></i> Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="<?php echo e(route('logout')); ?>">
                                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar + Content -->
    <div class="d-flex flex-grow-1">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar p-3 d-xl-block">
            
            <ul class="nav flex-column" id="erpSidebar">

                <li class="nav-item">
                    <a href="#" class="nav-link"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a>
                </li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#adminPanel" role="button">
                        <i class="fas fa-user-shield me-2"></i>Admin Panel
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse" id="adminPanel">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="#" class="nav-link">User Management</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Roles & Permissions</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Notifications</a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    <?php
                        $academicActive = request()->routeIs('classes.index')
                        || request()->routeIs('sections.index')
                        || request()->routeIs('subjects.index')
                        || request()->routeIs('class-subjects.index');
                    ?>
                    <a class="nav-link <?php echo e($academicActive ? '' : 'collapsed'); ?>" 
                       data-bs-toggle="collapse" 
                       href="#academic" 
                       role="button"
                       aria-expanded="<?php echo e($academicActive ? 'true' : 'false'); ?>">
                        <i class="fas fa-school me-2"></i>Academic Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse <?php echo e($academicActive ? 'show' : ''); ?>" id="academic">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="<?php echo e(route('classes.index')); ?>" class="nav-link 
                                <?php echo e(request()->routeIs('classes.index') ? 'active' : ''); ?>">Class List</a></li>
                            <li class="nav-item">
                                <a href="<?php echo e(route('sections.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('sections.index') ? 'active' : ''); ?>">Section List</a>
                            </li>
                            <li class="nav-item"><a href="<?php echo e(route('subjects.index')); ?>" class="nav-link
                                <?php echo e(request()->routeIs('subjects.index') ? 'active' : ''); ?>">Subject List</a></li>
                            <li class="nav-item"><a href="<?php echo e(route('class-subjects.index')); ?>" class="nav-link
                                <?php echo e(request()->routeIs('class-subjects.index') ? 'active' : ''); ?>">Class Subject List</a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    <?php
                        $teacherActive = request()->routeIs('teachers.index')
                        || request()->routeIs('teacher-subjects.index');
                    ?>
                    <a class="nav-link <?php echo e($teacherActive ? '' : 'collapsed'); ?>" 
                       data-bs-toggle="collapse" 
                       href="#teachers" 
                       role="button"
                       aria-expanded="<?php echo e($teacherActive ? 'true' : 'false'); ?>">
                        <i class="fas fa-chalkboard-teacher me-2"></i>Teacher Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse <?php echo e($teacherActive ? 'show' : ''); ?>" id="teachers">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="<?php echo e(route('teachers.index')); ?>" class="nav-link 
                                <?php echo e(request()->routeIs('teachers.index') ? 'active' : ''); ?>">Teacher List</a></li>
                            <li class="nav-item">
                                <a href="<?php echo e(route('teacher-subjects.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('teacher-subjects.index') ? 'active' : ''); ?>">Teacher Subject List</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    <?php
                        $teacherActive = request()->routeIs('students.index')
                        || request()->routeIs('student-academic-sessions.index');
                    ?>
                    <a class="nav-link <?php echo e($teacherActive ? '' : 'collapsed'); ?>" 
                       data-bs-toggle="collapse" 
                       href="#students" 
                       role="button"
                       aria-expanded="<?php echo e($teacherActive ? 'true' : 'false'); ?>">
                        <i class="fas fa-user-graduate me-2"></i> Student Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse <?php echo e($teacherActive ? 'show' : ''); ?>" id="students">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="<?php echo e(route('students.index')); ?>" class="nav-link 
                                <?php echo e(request()->routeIs('students.index') ? 'active' : ''); ?>">Student List</a></li>
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-academic-sessions.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('student-academic-sessions.index') ? 'active' : ''); ?>">Student Academic Session List</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    <?php
                        $timetableActive = request()->routeIs('periods.index')
                        || request()->routeIs('timetables.index');
                    ?>
                    <a class="nav-link <?php echo e($timetableActive ? '' : 'collapsed'); ?>" 
                       data-bs-toggle="collapse" 
                       href="#timetable" 
                       role="button"
                       aria-expanded="<?php echo e($timetableActive ? 'true' : 'false'); ?>">
                        <i class="fas fa-school me-2"></i>Timetable Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse <?php echo e($timetableActive ? 'show' : ''); ?>" id="timetable">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a href="<?php echo e(route('periods.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('periods.index') ? 'active' : ''); ?>">Period List</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo e(route('timetables.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('timetables.index') ? 'active' : ''); ?>">Timetable List</a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item">
                    <?php
                        $timetableActive = request()->routeIs('teacher-attendances.index')
                        || request()->routeIs('student-attendances.index');
                    ?>
                    <a class="nav-link <?php echo e($timetableActive ? '' : 'collapsed'); ?>" 
                       data-bs-toggle="collapse" 
                       href="#attendance" 
                       role="button"
                       aria-expanded="<?php echo e($timetableActive ? 'true' : 'false'); ?>">
                        <i class="fas fa-calendar-check me-2"></i>Attendance Management
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse <?php echo e($timetableActive ? 'show' : ''); ?>" id="attendance">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a href="<?php echo e(route('teacher-attendances.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('teacher-attendances.index') ? 'active' : ''); ?>">Teacher Attendance</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo e(route('student-attendances.index')); ?>" class="nav-link 
                                    <?php echo e(request()->routeIs('student-attendances.index') ? 'active' : ''); ?>">Student Attendance</a>
                            </li>
                        </ul>
                    </div>
                </li>

                
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-file-invoice-dollar me-2"></i>Fees Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-poll me-2"></i>Exams & Results</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-book me-2"></i>Library Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-hotel me-2"></i>Hostel Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-bus me-2"></i>Transport Management</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-bullhorn me-2"></i>Communication</a></li>
                <li class="nav-item"><a href="#" class="nav-link"><i class="fas fa-chart-line me-2"></i>Reports Module</a></li>

                <li class="nav-item">
                    <a class="nav-link collapsed" data-bs-toggle="collapse" href="#authSecurity" role="button">
                        <i class="fas fa-lock me-2"></i>Authentication
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse" id="authSecurity">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="#" class="nav-link">Login/Logout</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">Password Reset</a></li>
                            <li class="nav-item"><a href="#" class="nav-link">2FA Setup</a></li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item"><a href="<?php echo e(route('audit-logs.index')); ?>" class="nav-link <?php echo e(request()->routeIs('audit-logs.index') ? 'active' : ''); ?>"><i class="fas fa-shield-alt me-2"></i> Audit Log</a></li>

                <li class="nav-item">
                    <?php
                        $settingActive = request()->routeIs('roles.index')
                        || request()->routeIs('session-masters.index');
                        // || request()->routeIs('permissions.index') 
                        // || request()->routeIs('users.index');
                    ?>
                    <a class="nav-link <?php echo e($settingActive ? '' : 'collapsed'); ?>" 
                       data-bs-toggle="collapse" 
                       href="#setting" 
                       role="button"
                       aria-expanded="<?php echo e($settingActive ? 'true' : 'false'); ?>">
                        <i class="fas fa-lock me-2"></i>Setting
                        <i class="fas fa-chevron-down float-end"></i>
                    </a>
                    <div class="collapse <?php echo e($settingActive ? 'show' : ''); ?>" id="setting">
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item"><a href="<?php echo e(route('roles.index')); ?>" class="nav-link 
                                <?php echo e(request()->routeIs('roles.index') ? 'active' : ''); ?>">Role Master</a></li>
                            <li class="nav-item"><a href="<?php echo e(route('session-masters.index')); ?>" class="nav-link 
                                <?php echo e(request()->routeIs('session-masters.index') ? 'active' : ''); ?>">Session Master</a></li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>

        <!-- Overlay for mobile -->
        <div id="overlay" class="overlay"></div>

        <!-- Main Content -->
        <div class="content flex-grow-1">
            <?php echo $__env->yieldContent('content'); ?>
        </div>
    </div>

    
    <footer class="bg-dark text-white mt-auto py-3">
        <div class="container-fluid text-center">
            <small>
                 &copy; <?php echo e(date('Y')); ?> <?php echo e(config('constant.footer_company_name')); ?>. All rights reserved.
            </small>
        </div>
    </footer>

    <!-- Bootstrap Bundle JS -->
    <script src="<?php echo e(asset('assets/bootstrap/bootstrap.min.js')); ?>"></script>

    <script>
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const toggleBtn = document.getElementById('toggleSidebar');

        // Toggle sidebar
        toggleBtn?.addEventListener('click', (e) => {
            e.preventDefault();
            sidebar.classList.toggle('show');
            overlay.classList.toggle('show');
            document.body.style.overflow = sidebar.classList.contains('show') ? 'hidden' : '';
        });

        // Close sidebar when clicking overlay
        overlay?.addEventListener('click', () => {
            closeSidebar();
        });

        // Close sidebar function
        function closeSidebar() {
            sidebar.classList.remove('show');
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1200) {
                closeSidebar();
            }
        });

        // Close sidebar on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && sidebar.classList.contains('show')) {
                closeSidebar();
            }
        });

        // Prevent body scroll when sidebar is open on mobile
        sidebar.addEventListener('transitionend', () => {
            if (!sidebar.classList.contains('show')) {
                document.body.style.overflow = '';
            }
        });
    </script>

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <script>
        // Globally set
        alertify.set('notifier','position', 'top-right');
        alertify.defaults.theme.ok = "btn btn-outline-primary";
        alertify.defaults.theme.cancel = "btn btn-outline-secondary";

        // message
        // alertify.success('success');
        // alertify.warning('warning');
        // alertify.error('error');

        // alert for validation error
        // alertify.alert().setting({
        //     'title' : 'Validation Error!',
        //     'message' : 'Email is inavlid!'
        // }).show();

        
        // Auto-close after 5 second (5000 ms)
        // const alertInstance = alertify.alert()
            // .setting({
            //     'title': 'Validation Error!',
            //     'message': 'Email is invalid!',
            // }).show();
        // setTimeout(function () {
        //     alertInstance.close();
        // }, 5000);


        // confirm for delete
        // alertify.confirm('Confirm Delete?', 'Are you sure to delete?', function(){ 
        //     alertify.success('Ok') 
        // }, function(){
        //      alertify.error('Cancel')
        // });
    </script>

    
    <script src="<?php echo e(asset('assets/select/select2.min.js')); ?>"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                placeholder: "Search here...",
                allowClear: true
            });
        });
    </script>

    
    <script src="<?php echo e(asset('assets/jquery-ui/jquery-ui.min.js')); ?>"></script>
    <script>
        // Datepicker
        $(document).ready(function () {
            var currentYear = new Date().getFullYear();
            $('.datepicker').datepicker({
                dateFormat: "dd-mm-yy",
                changeMonth: true,
                changeYear: true
                // yearRange: "1940:2050", 
                // minDate: new Date(currentYear - 80, 0, 1), // Jan 1, currentYear - 80
                // maxDate: new Date(currentYear + 80, 11, 31) // Dec 31, currentYear + 80
            });
        });
    </script>

</body>
</html><?php /**PATH C:\xampp\htdocs\school_erp_laravel12\resources\views/layouts/admin.blade.php ENDPATH**/ ?>